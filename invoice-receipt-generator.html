<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发票/收据生成器 - SmartOffice 2.0</title>
    
    <!-- 外部依赖库 - 带错误处理 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"
            onerror="console.warn('html2canvas CDN加载失败，PDF导出功能可能受限')"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"
            onerror="console.warn('jsPDF CDN加载失败，PDF导出功能可能受限')"></script>
    
    <style>
        /**
         * @file 发票/收据生成器样式
         * @description 整合的发票收据专用样式文件
         */
        
        /* #region CSS变量定义 */
        :root {
            /* A4纸张标准尺寸 */
            --a4-width-px: 794px;
            --a4-height-px: 1123px;
            
            /* 布局变量 */
            --header-height: 120px;
            --footer-height: 120px;
            --content-padding: 20px;
            
            /* 页边距 */
            --margin-top-px: 100px;
            --margin-bottom-px: 80px;
            --margin-left-px: 37.8px;
            --margin-right-px: 37.8px;
            
            /* 颜色变量 */
            --primary-color: #1e40af;
            --secondary-color: #3b82f6;
            --accent-color: #f59e0b;
            --light-color: #f3f4f6;
            --dark-color: #1f2937;
            --background-color: #f9fafb;
            --text-color: #333333;
            --border-color: #e5e7eb;
            
            /* 字体变量 */
            --base-font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            --classic-font-family: 'Times New Roman', 'SimSun', serif;
            --elegant-font-family: 'Georgia', 'STZhongsong', serif;
            --base-font-size: 11pt;
            --title-font-size: 18pt;
            --small-font-size: 9pt;
            --line-height: 1.5;
            
            /* 阴影变量 */
            --box-shadow-light: 0 2px 4px rgba(0,0,0,0.1);
            --box-shadow-medium: 0 4px 15px -1px rgba(0, 0, 0, 0.1), 0 6px 8px -1px rgba(0, 0, 0, 0.05);
            
            /* 过渡变量 */
            --transition-fast: 0.15s ease;
            --transition-normal: 0.3s ease;
            
            /* Z-index层级 */
            --z-index-header: 100;
            --z-index-footer: 100;
            --z-index-stamp: 10000;
            
            /* 预览容器缩放比例 */
            --preview-scale-factor: 0.75;
            
            /* 印章定位 */
            --stamp-bottom-offset: 20%;
            --stamp-right-offset: 8%;
        }
        /* #endregion */
        
        /* #region 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--base-font-family);
            font-size: var(--base-font-size);
            line-height: var(--line-height);
            color: var(--text-color);
            background-color: var(--background-color);
        }
        
        /* 隐藏类 */
        .hidden {
            display: none !important;
        }
        /* #endregion */
        
        /* #region 布局样式 */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            align-items: start;
        }
        
        .form-section {
            background: white;
            border-radius: 8px;
            box-shadow: var(--box-shadow-medium);
            padding: 25px;
        }
        
        .preview-section {
            background: white;
            border-radius: 8px;
            box-shadow: var(--box-shadow-medium);
            padding: 25px;
        }
        /* #endregion */
        
        /* #region 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            transition: var(--transition-fast);
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .items-table th,
        .items-table td {
            padding: 8px;
            border: 1px solid var(--border-color);
            text-align: left;
        }
        
        .items-table th {
            background-color: var(--light-color);
            font-weight: 600;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-fast);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #1e3a8a;
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #2563eb;
        }
        
        .btn-success {
            background-color: #10b981;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #059669;
        }
        
        .btn-danger {
            background-color: #ef4444;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #dc2626;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        /* #endregion */
        
        /* #region A4纸张和文档容器样式 */
        .a4-page {
            width: var(--a4-width-px);
            min-height: var(--a4-height-px);
            background: white;
            margin: 0 auto;
            box-shadow: var(--box-shadow-light);
            position: relative;
            overflow: hidden;
        }
        
        #document-preview {
            width: 100%;
            max-width: var(--a4-width-px);
            min-height: var(--a4-height-px);
            height: auto;
            margin: 0 auto 30px;
            padding: 0;
            background-color: white;
            box-shadow: var(--box-shadow-medium);
            transform: scale(var(--preview-scale-factor));
            transform-origin: top center;
            transition: var(--transition-normal);
            position: relative;
            overflow: visible;
        }
        
        #document-container {
            position: relative;
            width: var(--a4-width-px);
            min-height: var(--a4-height-px);
            background: white;
            margin: 0 auto;
            padding-top: calc(var(--header-height) + 10px);
            padding-bottom: calc(var(--footer-height) + 10px);
            padding-left: var(--content-padding);
            padding-right: var(--content-padding);
            display: flex;
            flex-direction: column;
            font-size: var(--base-font-size);
            line-height: var(--line-height);
            color: var(--text-color);
            overflow: visible;
            transform-origin: top center;
        }
        
        #preview-container {
            background: #f5f5f5;
            padding: 20px;
            min-height: 600px;
            overflow: auto;
            border-radius: 6px;
        }
        /* #endregion */
        
        /* #region 页眉页脚样式 */
        .document-header,
        .document-header-image-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: var(--header-height);
            z-index: var(--z-index-header);
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--box-shadow-light);
            padding: 5px;
        }
        
        .document-header-image-container img {
            height: 120px;
            max-width: 100%;
            width: 100%;
            object-fit: contain;
            display: block;
        }
        
        .document-footer,
        .document-footer-content {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: var(--footer-height);
            z-index: var(--z-index-footer);
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            border-top: 1px solid var(--border-color);
        }
        
        .document-footer-content {
            width: 100%;
            text-align: center;
            font-size: 8pt;
            color: #666;
            line-height: 1.2;
            padding: 2px 5px;
        }
        
        /* 统一页脚样式 */
        .unified-document-footer.company-footer-image-container {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: auto;
            min-height: var(--footer-height);
            background-color: white;
            z-index: var(--z-index-footer);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-end;
            padding: 5px 5px 2px 5px;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
            margin-bottom: 0;
        }
        
        .unified-document-footer.company-footer-image-container img {
            height: 120px;
            max-width: 100%;
            width: 100%;
            object-fit: contain;
            margin-bottom: 2px;
        }
        /* #endregion */
        
        /* #region 印章样式 */
        .company-stamp {
            position: absolute;
            bottom: var(--stamp-bottom-offset);
            right: var(--stamp-right-offset);
            z-index: 50; /* 降低z-index，确保不遮盖总金额 */
            width: 120px;
            height: 120px;
            opacity: 0.6; /* 设置为60%透明度，实现半透明效果 */
            pointer-events: none; /* 避免阻挡其他元素的交互 */
        }

        .company-stamp img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        /* 印章占位符样式 */
        .stamp-placeholder {
            position: absolute;
            bottom: var(--stamp-bottom-offset);
            right: var(--stamp-right-offset);
            width: 120px;
            height: 120px;
            z-index: 50;
            opacity: 0.5; /* 占位符更透明 */
            pointer-events: none;
        }
        /* #endregion */

        /* #region Material Design 增强样式 */
        .total-amount-display {
            font-size: 18px;
            font-weight: bold;
            color: var(--primary-color);
            padding: 12px;
            background: rgba(30, 64, 175, 0.05);
            border-radius: 8px;
            border-left: 4px solid var(--primary-color);
        }

        .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .empty-preview-message {
            text-align: center;
            color: #666;
            margin-top: 200px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }

        /* Material Design Enhanced Styles */
        .form-section, .preview-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15), 0 2px 4px rgba(0,0,0,0.1);
            padding: 24px;
            transition: box-shadow 0.3s ease;
        }

        .form-section:hover, .preview-section:hover {
            box-shadow: 0 8px 24px rgba(0,0,0,0.15), 0 4px 8px rgba(0,0,0,0.1);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            background: white;
            box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1), 0 2px 8px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-transform: none;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background: linear-gradient(45deg, #1e40af, #3b82f6);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #6b7280, #9ca3af);
            color: white;
        }

        .btn-success {
            background: linear-gradient(45deg, #10b981, #34d399);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(45deg, #ef4444, #f87171);
            color: white;
        }

        /* 图片占位区域样式 */
        .image-placeholder {
            border: 2px dashed #bbb;
            background: linear-gradient(135deg, #f5f5f5, #eeeeee);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 12px;
            text-align: center;
            position: relative;
            border-radius: 6px;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
            font-weight: 500;
        }

        .header-placeholder {
            height: var(--header-height);
            margin-bottom: 10px;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: var(--z-index-header);
        }

        .footer-placeholder {
            height: var(--footer-height);
            margin-top: 10px;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: var(--z-index-footer);
        }

        .stamp-placeholder {
            width: 120px;
            height: 120px;
            position: absolute;
            bottom: var(--stamp-bottom-offset);
            right: var(--stamp-right-offset);
            z-index: var(--z-index-stamp);
            border-radius: 50%;
        }

        /* 电子生成标识样式 */
        .electronic-signature {
            position: absolute;
            bottom: calc(var(--footer-height) + 20px);
            left: 0;
            right: 0;
            text-align: center;
            font-size: 12px;
            color: #999;
            z-index: 5;
            font-style: italic;
        }

        /* 内容适配和响应式字体 */
        .content-adaptive {
            font-size: clamp(10px, 2vw, 12px);
            line-height: 1.4;
        }

        .content-adaptive h1 {
            font-size: clamp(16px, 3vw, 20px);
            margin-bottom: 10px;
        }

        .content-adaptive h2 {
            font-size: clamp(14px, 2.5vw, 16px);
            margin-bottom: 8px;
        }

        .items-table.adaptive {
            font-size: clamp(9px, 1.8vw, 11px);
        }

        .items-table.adaptive th,
        .items-table.adaptive td {
            padding: clamp(4px, 1vw, 8px);
        }
        /* #endregion */

        /* #region Header Styles */
        .main-title {
            text-align: center;
            margin-bottom: 30px;
            color: var(--primary-color);
            font-size: 28px;
            font-weight: 600;
        }

        .section-title {
            margin-bottom: 20px;
            color: var(--dark-color);
            font-size: 20px;
            font-weight: 600;
        }
        /* #endregion */

        /* #region 响应式设计 */
        /* 平板设备 */
        @media (max-width: 1024px) {
            .container {
                padding: 15px;
            }

            .grid {
                gap: 20px;
            }

            #document-preview {
                --preview-scale-factor: 0.65;
            }
        }

        /* 小屏幕设备 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .form-section,
            .preview-section {
                padding: 20px;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .btn-group {
                flex-direction: column;
                gap: 10px;
            }

            .btn {
                width: 100%;
                padding: 12px 20px;
                font-size: 16px;
            }

            #document-preview {
                --preview-scale-factor: 0.5;
            }
        }

        /* 移动设备 */
        @media (max-width: 480px) {
            .container {
                padding: 5px;
            }

            .form-section,
            .preview-section {
                padding: 15px;
            }

            .items-table {
                font-size: 12px;
            }

            .items-table th,
            .items-table td {
                padding: 6px 4px;
            }

            .items-table input {
                font-size: 12px;
                padding: 4px;
            }

            .btn {
                font-size: 14px;
                padding: 10px 15px;
            }

            #document-preview {
                --preview-scale-factor: 0.4;
            }

            h1 {
                font-size: 24px;
            }

            h2 {
                font-size: 20px;
            }
        }

        /* 触摸设备优化 */
        @media (hover: none) and (pointer: coarse) {
            .btn {
                min-height: 44px;
                font-size: 16px;
            }

            input, select, textarea {
                min-height: 44px;
                font-size: 16px;
            }

            .items-table input {
                min-height: 40px;
            }
        }

        /* 多订单管理器样式 */
        .multi-order-container {
            margin-bottom: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
        }

        .order-manager {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .order-tabs {
            display: flex;
            gap: 5px;
        }

        .order-tab {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .order-tab:hover {
            background: #e9ecef;
        }

        .order-tab.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .order-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .current-order-info {
            padding: 8px 12px;
            background: white;
            border-radius: 4px;
            border: 1px solid #ddd;
            font-size: 14px;
        }

        .current-order-info span {
            margin-right: 15px;
        }

        .order-badge {
            display: inline-block;
            padding: 2px 8px;
            background: var(--primary-color);
            color: white;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .order-column {
            width: 100px;
            text-align: center;
        }

        .order-info {
            font-size: 12px;
            color: #666;
            text-align: center;
        }

        /* 合并视图样式 */
        .combined-view .item-description input,
        .combined-view .item-quantity input,
        .combined-view .item-price input {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            cursor: default;
        }

        .combined-view .item-description input:focus,
        .combined-view .item-quantity input:focus,
        .combined-view .item-price input:focus {
            outline: none;
            box-shadow: none;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .order-manager {
                flex-direction: column;
                gap: 10px;
            }

            .order-tabs {
                flex-wrap: wrap;
            }

            .current-order-info span {
                display: block;
                margin-bottom: 5px;
            }
        }
        /* #endregion */
    </style>
</head>
<body>
    <div class="container">
        <h1 class="main-title">
            发票/收据生成器 / Invoice/Receipt Generator
        </h1>
        
        <div class="grid">
            <!-- 左侧：表单输入区域 -->
            <div class="form-section">
                <h2 class="section-title">文档信息输入 / Document Information</h2>

                <!-- AI智能填充区域 -->
                <div class="form-group" style="border: 2px dashed #3b82f6; border-radius: 8px; padding: 15px; margin-bottom: 25px; background: linear-gradient(135deg, #f0f9ff, #e0f2fe);">
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
                        <h3 style="margin: 0; color: #1e40af; font-size: 16px;">🤖 AI智能填充 / AI Smart Fill</h3>
                        <button type="button" class="btn btn-primary" id="ai-fill-btn" onclick="toggleAIFillPanel()">
                            隐藏AI填充 / Hide AI Fill
                        </button>
                    </div>
                    <div id="ai-fill-panel">
                        <div class="form-row" style="margin-bottom: 15px;">
                            <div class="form-group">
                                <label for="ai-text-input">文本输入 / Text Input</label>
                                <textarea id="ai-text-input" rows="4" placeholder="粘贴订单信息、客户详情或其他相关文本... / Paste order information, customer details or other relevant text..."></textarea>
                            </div>
                            <div class="form-group">
                                <label for="ai-image-input">图片上传 / Image Upload</label>
                                <input type="file" id="ai-image-input" accept="image/jpeg,image/png" style="margin-bottom: 10px;">
                                <small style="color: #666; font-size: 12px;">支持JPG/PNG格式，最大20MB / Support JPG/PNG, max 20MB</small>
                            </div>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success" onclick="processAIFill()">
                                <span id="ai-process-text">🧠 分析并填充 / Analyze & Fill</span>
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="clearAIInput()">
                                清空输入 / Clear Input
                            </button>
                        </div>
                        <div id="ai-status" class="hidden" style="margin-top: 10px; padding: 8px; border-radius: 4px; font-size: 13px;"></div>
                    </div>
                </div>

                <!-- 文档类型选择 -->
                <div class="form-group">
                    <label for="document-type">文档类型 / Document Type</label>
                    <select id="document-type">
                        <option value="receipt">收据 / Receipt</option>
                        <option value="invoice">发票 / Invoice</option>
                    </select>
                </div>

                <!-- 公司选择 -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="company-selector">公司 / Company</label>
                        <select id="company-selector">
                            <option value="gomyhire">GoMyHire</option>
                            <option value="sky-mirror">Sky Mirror</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="currency-selector">货币 / Currency</label>
                        <select id="currency-selector">
                            <option value="MYR">马来西亚令吉 (RM) / Malaysian Ringgit</option>
                            <option value="CNY">人民币 (¥) / Chinese Yuan</option>
                        </select>
                    </div>
                </div>

                <!-- 公司信息字段 -->
                <div class="form-group">
                    <label for="company-name">公司名称 / Company Name</label>
                    <input type="text" id="company-name" placeholder="请输入公司名称 / Enter company name">
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="tax-id">税号 / Tax ID</label>
                        <input type="text" id="tax-id" placeholder="请输入税号 / Enter tax ID">
                    </div>
                    <div class="form-group">
                        <label for="company-phone">公司电话 / Company Phone</label>
                        <input type="text" id="company-phone" placeholder="请输入公司电话 / Enter company phone">
                    </div>
                </div>

                <div class="form-group">
                    <label for="company-address">公司地址 / Company Address</label>
                    <input type="text" id="company-address" placeholder="请输入公司地址 / Enter company address">
                </div>

                <div class="form-group">
                    <label for="contact-person">负责人姓名 / Contact Person</label>
                    <input type="text" id="contact-person" placeholder="请输入负责人姓名 / Enter contact person name">
                </div>
                
                <!-- 基本信息 -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="document-number">单据号码 / Document Number</label>
                        <input type="text" id="document-number" placeholder="自动生成 / Auto Generate">
                    </div>
                    <div class="form-group">
                        <label for="document-date">日期 / Date</label>
                        <input type="date" id="document-date">
                    </div>
                </div>

                <!-- 客户信息 -->
                <div class="form-group">
                    <label for="customer-name">客户名称 / Customer Name</label>
                    <input type="text" id="customer-name" placeholder="请输入客户名称 / Enter customer name">
                </div>

                <div class="form-group">
                    <label for="channel">渠道 / Channel</label>
                    <input type="text" id="channel" placeholder="请输入渠道名称 / Enter channel name">
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="customer-phone">客户电话 / Customer Phone</label>
                        <input type="text" id="customer-phone" placeholder="请输入客户电话 / Enter customer phone">
                    </div>
                    <div class="form-group">
                        <label for="customer-email">客户邮箱 / Customer Email</label>
                        <input type="email" id="customer-email" placeholder="请输入客户邮箱 / Enter customer email">
                    </div>
                </div>

                <!-- 项目明细 -->
                <div class="form-group">
                    <label>项目明细 / Item Details</label>

                    <!-- 多订单管理器 -->
                    <div class="multi-order-container" id="multi-order-container" style="display: none;">
                        <div class="order-manager">
                            <div class="order-tabs" id="order-tabs">
                                <!-- 动态生成订单标签页 -->
                            </div>
                            <div class="order-controls">
                                <button type="button" class="btn btn-secondary btn-sm" onclick="addNewOrder()">+ 新增订单</button>
                                <select id="display-mode" onchange="switchDisplayMode(this.value)">
                                    <option value="combined">合并显示</option>
                                    <option value="separate">分别显示</option>
                                </select>
                                <button type="button" class="btn btn-info btn-sm" onclick="toggleMultiOrderMode()">切换模式</button>
                            </div>
                        </div>

                        <!-- 当前订单信息 -->
                        <div class="current-order-info" id="current-order-info">
                            <span>当前订单：<strong id="current-order-display">-</strong></span>
                            <span>客户：<strong id="current-customer-display">-</strong></span>
                        </div>
                    </div>

                    <table class="items-table" id="items-table">
                        <thead>
                            <tr>
                                <th class="order-column" id="order-column-header" style="display: none;">订单 / Order</th>
                                <th>项目描述 / Description</th>
                                <th>数量 / Qty</th>
                                <th>单价 / Price</th>
                                <th>金额 / Amount</th>
                                <th>操作 / Action</th>
                            </tr>
                        </thead>
                        <tbody id="items-tbody">
                            <tr>
                                <td class="order-column" style="display: none;"></td>
                                <td><input type="text" placeholder="项目描述 / Item description" class="item-description" title="项目描述 / Item description"></td>
                                <td><input type="number" value="1" min="1" class="item-quantity" placeholder="数量" title="数量 / Quantity"></td>
                                <td><input type="number" step="0.01" min="0" class="item-price" placeholder="单价" title="单价 / Price"></td>
                                <td class="item-amount">0.00</td>
                                <td><button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">删除 / Delete</button></td>
                            </tr>
                        </tbody>
                    </table>
                    <button type="button" class="btn btn-secondary" onclick="addItem()">添加项目 / Add Item</button>
                </div>

                <!-- 总金额 -->
                <div class="form-group">
                    <label>总金额 / Total Amount</label>
                    <div class="total-amount-display">
                        RM <span id="total-amount">0.00</span>
                    </div>
                </div>

                <!-- 备注 -->
                <div class="form-group">
                    <label for="notes">备注 / Notes</label>
                    <textarea id="notes" rows="3" placeholder="请输入备注信息 / Enter notes"></textarea>
                </div>

                <!-- 操作按钮 -->
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" onclick="updatePreview()">更新预览 / Update Preview</button>
                    <button type="button" class="btn btn-secondary" onclick="clearForm()">清空表单 / Clear Form</button>
                </div>
            </div>
            
            <!-- 右侧：预览区域 -->
            <div class="preview-section">
                <div class="preview-header">
                    <h2>文档预览 / Document Preview</h2>
                    <div class="btn-group">
                        <button type="button" class="btn btn-success" onclick="exportToPDF()">导出PDF / Export PDF</button>
                        <button type="button" class="btn btn-success" onclick="exportToImage()">导出图片 / Export Image</button>
                    </div>
                </div>

                <!-- 预览容器 -->
                <div id="preview-container">
                    <div id="document-preview" class="a4-page">
                        <div id="document-container">
                            <div class="empty-preview-message">
                                请填写表单信息并点击"更新预览"按钮<br>
                                Please fill in the form and click "Update Preview" button
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        /**
         * @file 发票/收据生成器JavaScript功能模块
         * @description 独立的发票收据生成器，包含所有必要的功能
         */

        // #region 图片资源管理对象
        /**
         * 图片资源管理器 - 存储所有图片的 base64 编码
         * @description 内置图片资源管理，确保独立HTML文件的完整性
         */
        const ImageBase64 = {
            /**
             * 公司标志图片 - 用于文档头部显示
             */
            logos: {
                'sky-mirror': '', // Sky Mirror World Tour 标志占位符
                'gomyhire': ''    // GoMyHire Travel 标志占位符
            },

            /**
             * 页眉图片 - 用于文档顶部装饰
             */
            headers: {
                'sky-mirror': '', // Sky Mirror 页眉图片占位符
                'gomyhire': ''    // GoMyHire 页眉图片占位符
            },

            /**
             * 页脚图片 - 用于文档底部装饰
             */
            footers: {
                'sky-mirror': '', // Sky Mirror 页脚图片占位符
                'gomyhire': ''    // GoMyHire 页脚图片占位符
            },

            /**
             * 印章图片 - 用于文档签章
             */
            stamps: {
                'sky-mirror': '', // Sky Mirror 印章占位符
                'gomyhire': ''    // GoMyHire 印章占位符
            },

            /**
             * 获取公司标志图片
             * @function getLogo - 根据公司代码获取对应的标志图片
             * @param {string} company - 公司代码 ('sky-mirror' 或 'gomyhire')
             * @returns {string} 图片的 base64 编码字符串
             */
            getLogo(company) {
                return this.logos[company] || '';
            },

            /**
             * 获取页眉图片
             * @function getHeader - 根据公司代码获取对应的页眉图片
             * @param {string} company - 公司代码 ('sky-mirror' 或 'gomyhire')
             * @returns {string} 图片的 base64 编码字符串
             */
            getHeader(company) {
                return this.headers[company] || '';
            },

            /**
             * 获取页脚图片
             * @function getFooter - 根据公司代码获取对应的页脚图片
             * @param {string} company - 公司代码 ('sky-mirror' 或 'gomyhire')
             * @returns {string} 图片的 base64 编码字符串
             */
            getFooter(company) {
                return this.footers[company] || '';
            },

            /**
             * 获取印章图片
             * @function getStamp - 根据公司代码获取对应的印章图片
             * @param {string} company - 公司代码 ('sky-mirror' 或 'gomyhire')
             * @returns {string} 图片的 base64 编码字符串
             */
            getStamp(company) {
                return this.stamps[company] || '';
            },

            /**
             * 更新图片资源
             * @function updateImage - 更新指定类型和公司的图片资源
             * @param {string} type - 图片类型 ('logo', 'header', 'footer', 'stamp')
             * @param {string} company - 公司代码 ('sky-mirror' 或 'gomyhire')
             * @param {string} base64 - 图片的 base64 编码
             */
            updateImage(type, company, base64) {
                switch (type) {
                    case 'logo':
                        this.logos[company] = base64;
                        break;
                    case 'header':
                        this.headers[company] = base64;
                        break;
                    case 'footer':
                        this.footers[company] = base64;
                        break;
                    case 'stamp':
                        this.stamps[company] = base64;
                        break;
                }
            }
        };
        // #endregion

        // #region 全局变量和配置
        /**
         * 全局配置对象
         * @description 存储应用程序的全局配置信息
         */
        const AppConfig = {
            currentCompany: 'gomyhire',     // 当前选中的公司
            currentDocumentType: 'receipt', // 当前文档类型
            currentCurrency: 'MYR',         // 当前货币类型
            itemCounter: 1,                 // 项目计数器
            autoPreview: true,              // 自动预览更新开关
            geminiApiKey: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s',  // Gemini API密钥（硬植入）

            // 多订单支持配置
            multiOrderMode: false,          // 是否启用多订单模式
            multiOrderData: [],             // 多订单数据存储
            currentOrderIndex: 0,           // 当前选中的订单索引
            displayMode: 'separate',        // 显示模式：'separate' | 'combined'
            orderCounter: 1                 // 订单计数器
        };

        /**
         * DOM元素缓存对象
         * @description 缓存常用DOM元素，减少重复查询，提升性能
         */
        const DOMCache = {
            // 表单元素缓存
            documentType: null,
            companySelector: null,
            currencySelector: null,
            documentNumber: null,
            documentDate: null,
            customerName: null,
            channel: null,
            customerPhone: null,
            customerEmail: null,
            notes: null,
            totalAmount: null,
            itemsTable: null,
            itemsTbody: null,

            // AI相关元素缓存
            aiFillPanel: null,
            aiFillBtn: null,
            aiTextInput: null,
            aiImageInput: null,
            aiStatus: null,
            aiProcessText: null,

            // 预览相关元素缓存
            documentContainer: null,
            documentPreview: null,
            previewContainer: null,

            /**
             * 初始化DOM缓存
             * @function initCache - 初始化所有DOM元素缓存
             */
            initCache() {
                // 表单元素
                this.documentType = document.getElementById('document-type');
                this.companySelector = document.getElementById('company-selector');
                this.currencySelector = document.getElementById('currency-selector');
                this.documentNumber = document.getElementById('document-number');
                this.documentDate = document.getElementById('document-date');
                this.customerName = document.getElementById('customer-name');
                this.channel = document.getElementById('channel');
                this.customerPhone = document.getElementById('customer-phone');
                this.customerEmail = document.getElementById('customer-email');
                this.notes = document.getElementById('notes');
                this.totalAmount = document.getElementById('total-amount');
                this.itemsTable = document.getElementById('items-table');
                this.itemsTbody = document.getElementById('items-tbody');

                // AI相关元素
                this.aiFillPanel = document.getElementById('ai-fill-panel');
                this.aiFillBtn = document.getElementById('ai-fill-btn');
                this.aiTextInput = document.getElementById('ai-text-input');
                this.aiImageInput = document.getElementById('ai-image-input');
                this.aiStatus = document.getElementById('ai-status');
                this.aiProcessText = document.getElementById('ai-process-text');

                // 预览相关元素
                this.documentContainer = document.getElementById('document-container');
                this.documentPreview = document.getElementById('document-preview');
                this.previewContainer = document.getElementById('preview-container');
            },

            /**
             * 获取缓存的DOM元素
             * @function get - 获取指定的DOM元素
             * @param {string} elementName - 元素名称
             * @returns {HTMLElement|null} DOM元素或null
             */
            get(elementName) {
                return this[elementName] || null;
            }
        };

        /**
         * 货币配置对象
         * @description 存储不同货币的显示信息和格式
         */
        const CurrencyConfig = {
            'MYR': {
                symbol: 'RM',
                name: '马来西亚令吉',
                englishName: 'Malaysian Ringgit',
                code: 'MYR'
            },
            'CNY': {
                symbol: '¥',
                name: '人民币',
                englishName: 'Chinese Yuan',
                code: 'CNY'
            }
        };



        /**
         * 公司信息配置
         * @description 存储不同公司的基本信息
         */
        const CompanyInfo = {
            'gomyhire': {
                name: 'GoMyHire Travel Sdn Bhd',
                address: 'Kuala Lumpur, Malaysia',
                phone: '+60 3-1234 5678',
                email: '<EMAIL>'
            },
            'sky-mirror': {
                name: 'Sky Mirror World Tour',
                address: 'Selangor, Malaysia',
                phone: '+60 3-8765 4321',
                email: '<EMAIL>'
            }
        };
        // #endregion

        // #region 工具函数
        /**
         * 性能优化的防抖函数管理器
         * @description 提供统一的防抖函数管理，避免重复创建，提升性能
         */
        const DebounceManager = {
            timers: new Map(),

            /**
             * 防抖函数
             * @function debounce - 防抖函数，延迟执行
             * @param {Function} func - 要执行的函数
             * @param {number} wait - 延迟时间（毫秒）
             * @param {string} key - 防抖键名，用于管理多个防抖函数
             * @returns {Function} 防抖后的函数
             */
            debounce(func, wait, key = 'default') {
                return (...args) => {
                    if (this.timers.has(key)) {
                        clearTimeout(this.timers.get(key));
                    }

                    const timer = setTimeout(() => {
                        this.timers.delete(key);
                        func.apply(this, args);
                    }, wait);

                    this.timers.set(key, timer);
                };
            },

            /**
             * 清除指定的防抖定时器
             * @function clear - 清除指定键名的防抖定时器
             * @param {string} key - 防抖键名
             */
            clear(key) {
                if (this.timers.has(key)) {
                    clearTimeout(this.timers.get(key));
                    this.timers.delete(key);
                }
            },

            /**
             * 清除所有防抖定时器
             * @function clearAll - 清除所有防抖定时器
             */
            clearAll() {
                this.timers.forEach(timer => clearTimeout(timer));
                this.timers.clear();
            }
        };

        /**
         * 兼容性防抖函数（保持向后兼容）
         * @function debounce - 防抖函数，避免频繁触发
         * @param {Function} func - 要防抖的函数
         * @param {number} wait - 等待时间（毫秒）
         * @returns {Function} 防抖后的函数
         */
        function debounce(func, wait) {
            return DebounceManager.debounce(func, wait, `legacy_${Date.now()}`);
        }

        /**
         * 生成唯一单据号码
         * @function generateDocumentNumber - 生成基于时间戳的唯一单据号码
         * @param {string} type - 文档类型 ('invoice' 或 'receipt')
         * @returns {string} 格式化的单据号码
         */
        function generateDocumentNumber(type) {
            const date = new Date();
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            const prefix = type === 'invoice' ? 'INV' : 'RCP';
            return `${prefix}${year}${month}${day}${hours}${minutes}${seconds}`;
        }

        /**
         * 格式化金额显示
         * @function formatCurrency - 将数字格式化为货币显示格式
         * @param {number} amount - 金额数值
         * @param {boolean} withSymbol - 是否包含货币符号
         * @returns {string} 格式化后的金额字符串
         */
        function formatCurrency(amount, withSymbol = false) {
            const formattedAmount = parseFloat(amount || 0).toFixed(2);
            if (withSymbol) {
                const currency = CurrencyConfig[AppConfig.currentCurrency];
                return `${currency.symbol} ${formattedAmount}`;
            }
            return formattedAmount;
        }

        /**
         * 获取当前货币符号
         * @function getCurrentCurrencySymbol - 获取当前选中货币的符号
         * @returns {string} 货币符号
         */
        function getCurrentCurrencySymbol() {
            return CurrencyConfig[AppConfig.currentCurrency].symbol;
        }

        /**
         * 切换货币类型
         * @function switchCurrency - 切换货币类型并更新显示
         * @param {string} currencyCode - 货币代码 ('MYR' 或 'CNY')
         */
        function switchCurrency(currencyCode) {
            if (CurrencyConfig[currencyCode]) {
                AppConfig.currentCurrency = currencyCode;

                // 保存到localStorage
                localStorage.setItem('smartoffice_currency', currencyCode);

                // 更新所有金额显示
                updateAllCurrencyDisplays();

                // 如果启用自动预览，更新预览
                if (AppConfig.autoPreview) {
                    safeDebouncedUpdatePreview();
                }

                console.log(`货币已切换为: ${CurrencyConfig[currencyCode].name} (${currencyCode})`);
            }
        }

        /**
         * 更新所有货币显示
         * @function updateAllCurrencyDisplays - 更新页面上所有的货币符号显示
         */
        function updateAllCurrencyDisplays() {
            const symbol = getCurrentCurrencySymbol();

            // 更新总金额显示
            const totalAmountContainer = document.querySelector('.total-amount-display');
            if (totalAmountContainer && DOMCache.totalAmount) {
                const currentAmount = DOMCache.totalAmount.textContent;
                totalAmountContainer.innerHTML = `${symbol} <span id="total-amount">${currentAmount}</span>`;
                // 重新缓存更新后的元素
                DOMCache.totalAmount = document.getElementById('total-amount');
            }
        }

        /**
         * 计算项目总金额
         * @function calculateItemAmount - 计算单个项目的总金额
         * @param {number} quantity - 数量
         * @param {number} price - 单价
         * @returns {number} 总金额
         */
        function calculateItemAmount(quantity, price) {
            return (parseFloat(quantity || 0) * parseFloat(price || 0));
        }

        /**
         * 计算所有项目的总金额
         * @function calculateTotalAmount - 计算所有项目的总金额
         * @returns {number} 总金额
         */
        function calculateTotalAmount() {
            let total = 0;

            console.log(`💰 开始计算总金额 - 模式状态:`, {
                多订单模式: AppConfig.multiOrderMode,
                显示模式: AppConfig.displayMode,
                订单数据长度: AppConfig.multiOrderData ? AppConfig.multiOrderData.length : 0,
                当前订单索引: AppConfig.currentOrderIndex
            });

            // 检查是否为多订单合并模式
            if (AppConfig.multiOrderMode && AppConfig.displayMode === 'combined') {
                // 合并模式：从订单数据中计算总金额
                if (AppConfig.multiOrderData && AppConfig.multiOrderData.length > 0) {
                    AppConfig.multiOrderData.forEach((order, orderIndex) => {
                        if (order.items && order.items.length > 0) {
                            order.items.forEach((item, itemIndex) => {
                                const itemAmount = item.amount || 0;
                                total += itemAmount;
                                console.log(`💰 订单${orderIndex + 1}-项目${itemIndex + 1}: ${item.description} = ${formatCurrency(itemAmount)}`);
                            });
                        }
                    });
                    console.log(`💰 合并模式总金额计算完成: ${formatCurrency(total)} (来自${AppConfig.multiOrderData.length}个订单)`);
                } else {
                    console.warn('⚠️ 合并模式但无订单数据，回退到表格计算');
                    total = calculateTotalFromTable();
                }
            } else {
                // 单订单模式或分别显示模式：从表格输入框计算
                total = calculateTotalFromTable();
            }

            console.log(`💰 最终总金额: ${formatCurrency(total)}`);
            return total;
        }

        /**
         * 从表格计算总金额
         * @function calculateTotalFromTable - 从表格输入框计算总金额
         * @returns {number} 总金额
         */
        function calculateTotalFromTable() {
            let total = 0;
            const rows = document.querySelectorAll('#items-tbody tr');

            console.log(`💰 从表格计算总金额 - 表格行数: ${rows.length}`);

            rows.forEach((row, index) => {
                const quantityInput = row.querySelector('.item-quantity');
                const priceInput = row.querySelector('.item-price');
                const descriptionInput = row.querySelector('.item-description');

                if (quantityInput && priceInput && descriptionInput) {
                    const quantity = parseFloat(quantityInput.value || 0);
                    const price = parseFloat(priceInput.value || 0);
                    const description = descriptionInput.value.trim();
                    const itemAmount = calculateItemAmount(quantity, price);

                    // 只计算有描述的项目
                    if (description && quantity > 0 && price >= 0) {
                        total += itemAmount;
                        console.log(`💰 表格行${index + 1}: ${description} (${quantity} × ${price}) = ${formatCurrency(itemAmount)}`);
                    } else {
                        console.log(`⚠️ 表格行${index + 1}: 跳过空项目或无效数据 (描述: "${description}", 数量: ${quantity}, 价格: ${price})`);
                    }
                } else {
                    console.warn(`⚠️ 表格行${index + 1}: 缺少必要的输入框元素`);
                }
            });

            console.log(`💰 表格模式总金额计算完成: ${formatCurrency(total)}`);
            return total;
        }

        /**
         * 更新项目金额显示
         * @function updateItemAmount - 更新单个项目的金额显示
         * @param {HTMLElement} row - 项目行元素
         */
        function updateItemAmount(row) {
            const quantity = parseFloat(row.querySelector('.item-quantity').value || 0);
            const price = parseFloat(row.querySelector('.item-price').value || 0);
            const amount = calculateItemAmount(quantity, price);

            row.querySelector('.item-amount').textContent = formatCurrency(amount);
            updateTotalAmount();
        }

        /**
         * 更新总金额显示
         * @function updateTotalAmount - 更新总金额显示
         */
        function updateTotalAmount() {
            const total = calculateTotalAmount();
            if (DOMCache.totalAmount) {
                DOMCache.totalAmount.textContent = formatCurrency(total);
            }

            // 如果启用自动预览，触发预览更新
            if (AppConfig.autoPreview) {
                safeDebouncedUpdatePreview();
            }
        }

        /**
         * 收集表单数据（包含新增字段）
         * @function collectFormData - 收集所有表单输入数据
         * @returns {Object} 表单数据对象
         */
        function collectFormData() {
            const items = [];
            const rows = DOMCache.itemsTbody ? DOMCache.itemsTbody.querySelectorAll('tr') : document.querySelectorAll('#items-tbody tr');

            console.log(`📊 收集表单数据 - 模式: ${AppConfig.multiOrderMode ? '多订单' : '单订单'}, 显示: ${AppConfig.displayMode || '标准'}`);

            rows.forEach(row => {
                const description = row.querySelector('.item-description').value.trim();
                const quantity = parseFloat(row.querySelector('.item-quantity').value || 0);
                const price = parseFloat(row.querySelector('.item-price').value || 0);

                if (description && quantity > 0 && price >= 0) {
                    items.push({
                        description,
                        quantity,
                        price,
                        amount: calculateItemAmount(quantity, price)
                    });
                }
            });

            const docType = DOMCache.documentType ? DOMCache.documentType.value : document.getElementById('document-type').value;

            return {
                documentType: docType,
                documentNumber: (DOMCache.documentNumber ? DOMCache.documentNumber.value : document.getElementById('document-number').value) || generateDocumentNumber(docType),
                date: (DOMCache.documentDate ? DOMCache.documentDate.value : document.getElementById('document-date').value) || new Date().toISOString().split('T')[0],

                // 公司信息
                companyName: (document.getElementById('company-name') || {}).value?.trim() || '',
                taxId: (document.getElementById('tax-id') || {}).value?.trim() || '',
                companyAddress: (document.getElementById('company-address') || {}).value?.trim() || '',
                companyPhone: (document.getElementById('company-phone') || {}).value?.trim() || '',
                contactPerson: (document.getElementById('contact-person') || {}).value?.trim() || '',

                // 客户信息
                customerName: DOMCache.customerName ? DOMCache.customerName.value.trim() : '',
                channel: DOMCache.channel ? DOMCache.channel.value.trim() : '',
                customerPhone: DOMCache.customerPhone ? DOMCache.customerPhone.value.trim() : '',
                customerEmail: DOMCache.customerEmail ? DOMCache.customerEmail.value.trim() : '',

                items: items,
                total: calculateTotalAmount(),
                notes: DOMCache.notes ? DOMCache.notes.value.trim() : ''
            };

            console.log(`📊 表单数据收集完成 - 项目数: ${items.length}, 总金额: ${formatCurrency(data.total)}`);
            return data;
        }

        /**
         * 控制公司信息字段显示
         * @function toggleCompanyFields - 根据文档类型显示或隐藏公司信息字段
         * @param {string} documentType - 文档类型 ('invoice' 或 'receipt')
         */
        function toggleCompanyFields(documentType) {
            const companyFields = [
                'company-name',
                'tax-id',
                'company-address',
                'company-phone',
                'contact-person'
            ];

            companyFields.forEach(fieldId => {
                const fieldGroup = document.getElementById(fieldId)?.closest('.form-group');
                if (fieldGroup) {
                    if (documentType === 'invoice') {
                        fieldGroup.style.display = 'block';
                        fieldGroup.classList.remove('hidden');
                    } else {
                        fieldGroup.style.display = 'none';
                        fieldGroup.classList.add('hidden');
                    }
                }
            });

            // 对于form-row中的字段，需要特殊处理
            const taxIdGroup = document.getElementById('tax-id')?.closest('.form-group');
            const phoneGroup = document.getElementById('company-phone')?.closest('.form-group');

            if (taxIdGroup && phoneGroup) {
                const formRow = taxIdGroup.closest('.form-row');
                if (formRow) {
                    if (documentType === 'invoice') {
                        formRow.style.display = 'grid';
                        formRow.classList.remove('hidden');
                    } else {
                        formRow.style.display = 'none';
                        formRow.classList.add('hidden');
                    }
                }
            }

            console.log(`字段显示已更新: ${documentType === 'invoice' ? '显示' : '隐藏'}公司信息字段`);
        }

        /**
         * AI智能填充功能模块
         * @description 集成Gemini AI进行智能表单填充
         */

        /**
         * 切换AI填充面板显示
         * @function toggleAIFillPanel - 显示或隐藏AI填充面板
         */
        function toggleAIFillPanel() {
            const panel = DOMCache.aiFillPanel || document.getElementById('ai-fill-panel');
            const btn = DOMCache.aiFillBtn || document.getElementById('ai-fill-btn');

            if (panel && btn) {
                if (panel.classList.contains('hidden')) {
                    panel.classList.remove('hidden');
                    btn.textContent = '隐藏AI填充 / Hide AI Fill';
                } else {
                    panel.classList.add('hidden');
                    btn.textContent = '启用AI填充 / Enable AI Fill';
                }
            }
        }

        /**
         * 清空AI输入
         * @function clearAIInput - 清空AI输入框内容
         */
        function clearAIInput() {
            if (DOMCache.aiTextInput) DOMCache.aiTextInput.value = '';
            if (DOMCache.aiImageInput) DOMCache.aiImageInput.value = '';
            hideAIStatus();
        }

        /**
         * 显示AI处理状态
         * @function showAIStatus - 显示AI处理状态信息
         * @param {string} message - 状态消息
         * @param {string} type - 状态类型 ('info', 'success', 'error')
         */
        function showAIStatus(message, type = 'info') {
            const statusDiv = DOMCache.aiStatus || document.getElementById('ai-status');
            if (statusDiv) {
                statusDiv.classList.remove('hidden');
                statusDiv.textContent = message;

                // 设置状态样式
                statusDiv.className = 'ai-status-' + type;
                const styles = {
                    info: { backgroundColor: '#e0f2fe', color: '#0369a1', border: '1px solid #7dd3fc' },
                    success: { backgroundColor: '#dcfce7', color: '#166534', border: '1px solid #86efac' },
                    error: { backgroundColor: '#fee2e2', color: '#dc2626', border: '1px solid #fca5a5' }
                };

                const style = styles[type] || styles.info;
                Object.assign(statusDiv.style, style);
            }
        }

        /**
         * 隐藏AI状态
         * @function hideAIStatus - 隐藏AI状态显示
         */
        function hideAIStatus() {
            const statusDiv = DOMCache.aiStatus || document.getElementById('ai-status');
            if (statusDiv) {
                statusDiv.classList.add('hidden');
            }
        }

        /**
         * 处理AI智能填充
         * @function processAIFill - 处理AI智能填充请求
         */
        async function processAIFill() {
            const textInput = DOMCache.aiTextInput ? DOMCache.aiTextInput.value.trim() : '';
            const imageInput = DOMCache.aiImageInput ? DOMCache.aiImageInput.files[0] : null;

            if (!textInput && !imageInput) {
                showAIStatus('请输入文本或上传图片 / Please enter text or upload image', 'error');
                return;
            }

            const processBtn = DOMCache.aiProcessText || document.getElementById('ai-process-text');
            const originalText = processBtn ? processBtn.textContent : '🧠 分析并填充 / Analyze & Fill';

            try {
                // 显示处理状态
                processBtn.textContent = '🔄 分析中... / Analyzing...';
                showAIStatus('正在连接Gemini AI进行智能分析... / Connecting to Gemini AI for analysis...', 'info');

                let analysisResult;

                if (imageInput) {
                    analysisResult = await processImageWithGemini(imageInput, textInput);
                } else {
                    analysisResult = await processTextWithGemini(textInput);
                }

                if (analysisResult) {
                    await applyAIResults(analysisResult);
                    showAIStatus('AI分析完成，信息已填充到表单 / AI analysis completed, information filled to form', 'success');
                } else {
                    showAIStatus('AI分析未能提取有效信息，请检查输入内容 / AI analysis could not extract valid information', 'error');
                }

            } catch (error) {
                console.error('AI处理错误:', error);
                showAIStatus(`AI处理失败: ${error.message} / AI processing failed: ${error.message}`, 'error');
            } finally {
                processBtn.textContent = originalText;
            }
        }

        /**
         * 使用Gemini AI处理文本
         * @function processTextWithGemini - 使用Gemini AI分析文本内容
         * @param {string} text - 要分析的文本
         * @returns {Object|null} 分析结果对象
         */
        async function processTextWithGemini(text) {
            const prompt = `
请分析以下文本，提取相关的发票/收据信息，并以JSON格式返回。请提取以下字段（如果存在）：

字段说明：
- companyName: 公司名称
- taxId: 税号
- companyAddress: 公司地址
- companyPhone: 公司电话
- contactPerson: 负责人姓名
- documentNumber: 单据号码（重点识别以下类型的订单标识符）
  * Order ID / 订单ID
  * 订单编号 / Order Number
  * 单号 / Document Number
  * OTA Reference / OTA参考号
  * Booking Reference / 预订参考号
  * Transaction ID / 交易ID
  * Invoice Number / 发票号
  * Receipt Number / 收据号
  * 支持中英文混合识别
  * 识别常见编号格式：字母+数字组合、纯数字、带分隔符的编号等
  * 处理不同标签格式：冒号、等号、空格分隔等
- customerName: 客户名称
- channel: 渠道名称
- customerPhone: 客户电话
- customerEmail: 客户邮箱
- items: 项目数组，每个项目包含 {description, quantity, price}
- notes: 备注信息

要分析的文本：
${text}

请只返回JSON格式的结果，不要包含其他解释文字。如果某个字段无法提取，请设为空字符串或空数组。
特别注意：请仔细查找文本中的各种单据号码、订单号、参考号等标识符，并提取到documentNumber字段中。
`;

            console.log('🔍 Gemini文本分析 - 输入文本:', text.substring(0, 200) + (text.length > 200 ? '...' : ''));

            try {
                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${AppConfig.geminiApiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: prompt
                            }]
                        }]
                    })
                });

                if (!response.ok) {
                    throw new Error(`Gemini API请求失败: ${response.status}`);
                }

                const data = await response.json();
                const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;

                console.log('🤖 Gemini文本分析 - 原始响应:', generatedText);

                if (generatedText) {
                    // 尝试解析JSON - 支持对象和数组两种格式
                    let jsonMatch = generatedText.match(/\[[\s\S]*\]/); // 先尝试数组格式
                    if (!jsonMatch) {
                        jsonMatch = generatedText.match(/\{[\s\S]*\}/); // 再尝试对象格式
                    }

                    if (jsonMatch) {
                        try {
                            const parsedResult = JSON.parse(jsonMatch[0]);
                            console.log('📋 Gemini文本分析 - 解析结果:', parsedResult);

                            // 检查是否为多订单数组
                            if (Array.isArray(parsedResult)) {
                                console.log(`🔢 检测到多订单: ${parsedResult.length}个订单`);
                                parsedResult.forEach((order, index) => {
                                    console.log(`📋 订单${index + 1} - 单据号码:`, order.documentNumber || '未找到');
                                });
                                return { isMultiOrder: true, orders: parsedResult };
                            } else {
                                console.log('🔢 提取的单据号码:', parsedResult.documentNumber || '未找到');
                                return { isMultiOrder: false, ...parsedResult };
                            }
                        } catch (parseError) {
                            console.error('❌ JSON解析失败:', parseError);
                            console.log('🔍 尝试修复JSON格式...');

                            // 尝试修复常见的JSON格式问题
                            const cleanedJson = this.cleanJsonString(jsonMatch[0]);
                            try {
                                const parsedResult = JSON.parse(cleanedJson);
                                console.log('✅ JSON修复成功，解析结果:', parsedResult);

                                if (Array.isArray(parsedResult)) {
                                    return { isMultiOrder: true, orders: parsedResult };
                                } else {
                                    return { isMultiOrder: false, ...parsedResult };
                                }
                            } catch (secondError) {
                                console.error('❌ JSON修复失败:', secondError);
                                return null;
                            }
                        }
                    }
                }

                console.warn('⚠️ Gemini文本分析 - 未能解析有效JSON');
                return null;
            } catch (error) {
                console.error('❌ Gemini文本分析错误:', error);
                throw error;
            }
        }

        /**
         * 清理JSON字符串
         * @function cleanJsonString - 清理和修复常见的JSON格式问题
         * @param {string} jsonStr - 原始JSON字符串
         * @returns {string} 清理后的JSON字符串
         */
        function cleanJsonString(jsonStr) {
            return jsonStr
                .replace(/```json\s*/g, '') // 移除markdown代码块标记
                .replace(/```\s*/g, '')     // 移除结束的代码块标记
                .replace(/,\s*}/g, '}')     // 移除对象末尾多余的逗号
                .replace(/,\s*]/g, ']')     // 移除数组末尾多余的逗号
                .replace(/\n/g, ' ')        // 替换换行符为空格
                .replace(/\r/g, ' ')        // 替换回车符为空格
                .replace(/\t/g, ' ')        // 替换制表符为空格
                .replace(/\s+/g, ' ')       // 合并多个空格
                .trim();                    // 移除首尾空格
        }

        /**
         * 使用Gemini AI处理图片
         * @function processImageWithGemini - 使用Gemini AI分析图片内容
         * @param {File} imageFile - 图片文件
         * @param {string} additionalText - 额外的文本信息
         * @returns {Object|null} 分析结果对象
         */
        async function processImageWithGemini(imageFile, additionalText = '') {
            // 将图片转换为base64
            const base64Image = await fileToBase64(imageFile);
            const base64Data = base64Image.split(',')[1]; // 移除data:image/...;base64,前缀

            const prompt = `
请分析这张图片${additionalText ? '和以下文本' : ''}，提取相关的发票/收据信息，并以JSON格式返回。

${additionalText ? `额外文本信息：${additionalText}` : ''}

请提取以下字段（如果存在）：
- companyName: 公司名称
- taxId: 税号
- companyAddress: 公司地址
- companyPhone: 公司电话
- contactPerson: 负责人姓名
- documentNumber: 单据号码（重点识别以下类型的订单标识符）
  * Order ID / 订单ID
  * 订单编号 / Order Number
  * 单号 / Document Number
  * OTA Reference / OTA参考号
  * Booking Reference / 预订参考号
  * Transaction ID / 交易ID
  * Invoice Number / 发票号
  * Receipt Number / 收据号
  * 支持中英文混合识别
  * 识别常见编号格式：字母+数字组合、纯数字、带分隔符的编号等
  * 处理不同标签格式：冒号、等号、空格分隔等
- customerName: 客户名称
- channel: 渠道名称
- customerPhone: 客户电话
- customerEmail: 客户邮箱
- items: 项目数组，每个项目包含 {description, quantity, price}
- notes: 备注信息

请只返回JSON格式的结果，不要包含其他解释文字。如果某个字段无法提取，请设为空字符串或空数组。
特别注意：请仔细查找图片中的各种单据号码、订单号、参考号等标识符，并提取到documentNumber字段中。
`;

            console.log('🖼️ Gemini图片分析 - 开始处理图片:', imageFile.name, '大小:', (imageFile.size / 1024).toFixed(2) + 'KB');
            if (additionalText) {
                console.log('📝 额外文本信息:', additionalText.substring(0, 100) + (additionalText.length > 100 ? '...' : ''));
            }

            try {
                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${AppConfig.geminiApiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [
                                {
                                    text: prompt
                                },
                                {
                                    inline_data: {
                                        mime_type: imageFile.type,
                                        data: base64Data
                                    }
                                }
                            ]
                        }]
                    })
                });

                if (!response.ok) {
                    throw new Error(`Gemini API请求失败: ${response.status}`);
                }

                const data = await response.json();
                const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;

                console.log('🤖 Gemini图片分析 - 原始响应:', generatedText);

                if (generatedText) {
                    // 尝试解析JSON - 支持对象和数组两种格式
                    let jsonMatch = generatedText.match(/\[[\s\S]*\]/); // 先尝试数组格式
                    if (!jsonMatch) {
                        jsonMatch = generatedText.match(/\{[\s\S]*\}/); // 再尝试对象格式
                    }

                    if (jsonMatch) {
                        try {
                            const parsedResult = JSON.parse(jsonMatch[0]);
                            console.log('📋 Gemini图片分析 - 解析结果:', parsedResult);

                            // 检查是否为多订单数组
                            if (Array.isArray(parsedResult)) {
                                console.log(`🔢 检测到多订单: ${parsedResult.length}个订单`);
                                parsedResult.forEach((order, index) => {
                                    console.log(`📋 订单${index + 1} - 单据号码:`, order.documentNumber || '未找到');
                                });
                                return { isMultiOrder: true, orders: parsedResult };
                            } else {
                                console.log('🔢 提取的单据号码:', parsedResult.documentNumber || '未找到');
                                return { isMultiOrder: false, ...parsedResult };
                            }
                        } catch (parseError) {
                            console.error('❌ JSON解析失败:', parseError);
                            console.log('🔍 尝试修复JSON格式...');

                            // 尝试修复常见的JSON格式问题
                            const cleanedJson = cleanJsonString(jsonMatch[0]);
                            try {
                                const parsedResult = JSON.parse(cleanedJson);
                                console.log('✅ JSON修复成功，解析结果:', parsedResult);

                                if (Array.isArray(parsedResult)) {
                                    return { isMultiOrder: true, orders: parsedResult };
                                } else {
                                    return { isMultiOrder: false, ...parsedResult };
                                }
                            } catch (secondError) {
                                console.error('❌ JSON修复失败:', secondError);
                                return null;
                            }
                        }
                    }
                }

                console.warn('⚠️ Gemini图片分析 - 未能解析有效JSON');
                return null;
            } catch (error) {
                console.error('❌ Gemini图片分析错误:', error);
                throw error;
            }
        }

        /**
         * 将文件转换为base64
         * @function fileToBase64 - 将文件转换为base64编码
         * @param {File} file - 文件对象
         * @returns {Promise<string>} base64编码字符串
         */
        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => resolve(reader.result);
                reader.onerror = error => reject(error);
            });
        }

        /**
         * 应用AI分析结果到表单
         * @function applyAIResults - 将AI分析结果填充到表单字段
         * @param {Object} results - AI分析结果对象
         */
        async function applyAIResults(results) {
            if (!results) return;

            console.log('🔄 开始应用AI分析结果到表单:', results);

            // 修复：正确检查是否为多订单结果
            if (results.isMultiOrder && results.orders && Array.isArray(results.orders)) {
                // 只有当数组包含多个订单时才使用多订单模式
                if (results.orders.length > 1) {
                    console.log(`🔄 检测到多订单: ${results.orders.length}个订单，启用多订单模式`);
                    await handleMultiOrderResults(results.orders);
                    return;
                } else if (results.orders.length === 1) {
                    console.log('🔄 检测到单订单（数组格式），使用单订单模式处理');
                    await applySingleOrderResults(results.orders[0]);
                    return;
                }
            }

            // 单订单处理（移除isMultiOrder标记）
            const singleOrderData = { ...results };
            delete singleOrderData.isMultiOrder;
            await applySingleOrderResults(singleOrderData);
        }

        /**
         * 处理多订单AI结果
         * @function handleMultiOrderResults - 处理多个订单的AI分析结果
         * @param {Array} orders - 订单数组
         */
        async function handleMultiOrderResults(orders) {
            console.log(`🔄 开始处理多订单AI结果: ${orders.length}个订单`);

            // 修复：只有真正的多订单才切换模式
            if (orders.length <= 1) {
                console.log('⚠️ 订单数量不足，不应使用多订单模式，转为单订单处理');
                if (orders.length === 1) {
                    await applySingleOrderResults(orders[0]);
                }
                return;
            }

            // 智能模式切换：自动切换到多订单模式
            if (!AppConfig.multiOrderMode) {
                console.log('🔄 智能模式切换：检测到多订单，自动切换到多订单模式');
                MultiOrderManager.toggleMode();
            }

            // 确保模式状态正确设置
            AppConfig.multiOrderMode = true;
            console.log(`✅ 多订单模式状态确认: ${AppConfig.multiOrderMode}`);

            // 清空现有订单数据
            AppConfig.multiOrderData = [];
            AppConfig.currentOrderIndex = 0;

            // 处理每个订单
            for (let i = 0; i < orders.length; i++) {
                const orderData = orders[i];
                console.log(`📋 处理订单 ${i + 1}/${orders.length}:`, orderData.documentNumber || `订单${i + 1}`);

                // 创建订单对象，确保项目金额正确计算
                const processedItems = (orderData.items || []).map(item => ({
                    ...item,
                    amount: calculateItemAmount(item.quantity || 1, item.price || 0)
                }));

                const newOrder = {
                    orderId: orderData.documentNumber || `ORD${String(AppConfig.orderCounter).padStart(3, '0')}`,
                    orderName: `订单${i + 1}`,
                    documentNumber: orderData.documentNumber || '',
                    customerName: orderData.customerName || '',
                    customerPhone: orderData.customerPhone || '',
                    customerEmail: orderData.customerEmail || '',
                    items: processedItems,
                    notes: orderData.notes || ''
                };

                AppConfig.multiOrderData.push(newOrder);
                AppConfig.orderCounter++;
            }

            // 切换到第一个订单
            AppConfig.currentOrderIndex = 0;
            MultiOrderManager.loadOrderToForm(AppConfig.multiOrderData[0]);
            MultiOrderManager.updateOrderTabs();
            MultiOrderManager.updateCurrentOrderDisplay();

            // 设置默认显示模式为合并显示
            AppConfig.displayMode = 'combined';
            const displayModeSelector = document.getElementById('display-mode');
            if (displayModeSelector) {
                displayModeSelector.value = 'combined';
            }

            // 自动切换到合并显示模式
            MultiOrderManager.showCombinedView();

            // 填充公司信息（所有订单共享）
            const firstOrder = orders[0];
            fillCompanyInfo(firstOrder);

            // 智能切换功能
            await performIntelligentSwitching(firstOrder);

            // 更新总金额和预览
            updateTotalAmount();
            if (AppConfig.autoPreview) {
                safeDebouncedUpdatePreview();
            }

            console.log(`🎉 多订单AI结果处理完成: ${orders.length}个订单已创建`);

            // 输出多订单摘要
            const multiOrderSummary = orders.map((order, index) => ({
                序号: index + 1,
                单据号码: order.documentNumber || '未识别',
                客户名称: order.customerName || '未识别',
                项目数量: (order.items && order.items.length) || 0
            }));
            console.table(multiOrderSummary);
        }

        /**
         * 应用单订单AI结果
         * @function applySingleOrderResults - 应用单个订单的AI分析结果
         * @param {Object} results - 单订单AI分析结果
         */
        async function applySingleOrderResults(results) {
            console.log('🔄 开始应用单订单AI结果:', results);

            // 智能模式切换：确保单订单模式
            if (AppConfig.multiOrderMode) {
                console.log('🔄 智能模式切换：检测到单订单，当前为多订单模式');
                console.log('ℹ️ 保持多订单模式，单订单数据将添加到当前订单');
            } else {
                console.log('✅ 当前为单订单模式，直接应用数据');
            }

            // 填充单据号码（优先处理）
            if (results.documentNumber) {
                const documentNumberInput = DOMCache.documentNumber || document.getElementById('document-number');
                if (documentNumberInput) {
                    const currentValue = documentNumberInput.value.trim();
                    // 如果当前单据号码为空，或者是自动生成的格式（INV/RCP开头），则使用AI识别的号码
                    if (!currentValue || currentValue.match(/^(INV|RCP)\d{14}$/)) {
                        documentNumberInput.value = results.documentNumber;
                        console.log('✅ 单据号码已填充:', results.documentNumber);
                    } else {
                        console.log('ℹ️ 单据号码已存在，跳过填充:', currentValue);
                    }
                }
            } else {
                console.log('⚠️ AI未识别到单据号码');
            }

            // 填充公司信息
            fillCompanyInfo(results);

            // 填充客户信息
            if (results.customerName && DOMCache.customerName) {
                DOMCache.customerName.value = results.customerName;
                console.log('✅ 客户名称已填充:', results.customerName);
            }
            if (results.channel && DOMCache.channel) {
                DOMCache.channel.value = results.channel;
                console.log('✅ 渠道已填充:', results.channel);
            }
            if (results.customerPhone && DOMCache.customerPhone) {
                DOMCache.customerPhone.value = results.customerPhone;
                console.log('✅ 客户电话已填充:', results.customerPhone);
            }
            if (results.customerEmail && DOMCache.customerEmail) {
                DOMCache.customerEmail.value = results.customerEmail;
                console.log('✅ 客户邮箱已填充:', results.customerEmail);
            }

            // 填充备注
            if (results.notes && DOMCache.notes) {
                DOMCache.notes.value = results.notes;
                console.log('✅ 备注已填充:', results.notes);
            }

            // 处理项目数据
            if (results.items && Array.isArray(results.items) && results.items.length > 0) {
                await fillItemsFromAI(results.items);
                console.log('✅ 项目数据已填充:', results.items.length + '个项目');
            }

            // 智能切换功能
            await performIntelligentSwitching(results);

            // 更新总金额和预览
            updateTotalAmount();
            if (AppConfig.autoPreview) {
                safeDebouncedUpdatePreview();
            }

            console.log('🎉 AI分析结果已全部应用到表单完成');

            // 输出填充摘要
            const fillSummary = {
                单据号码: results.documentNumber || '未识别',
                公司名称: results.companyName || '未识别',
                客户名称: results.customerName || '未识别',
                项目数量: (results.items && results.items.length) || 0,
                备注: results.notes ? '已填充' : '未识别'
            };
            console.table(fillSummary);
        }

        /**
         * 填充公司信息
         * @function fillCompanyInfo - 填充公司相关信息到表单
         * @param {Object} results - 包含公司信息的结果对象
         */
        function fillCompanyInfo(results) {
            if (results.companyName) {
                document.getElementById('company-name').value = results.companyName;
                console.log('✅ 公司名称已填充:', results.companyName);
            }
            if (results.taxId) {
                document.getElementById('tax-id').value = results.taxId;
                console.log('✅ 税号已填充:', results.taxId);
            }
            if (results.companyAddress) {
                document.getElementById('company-address').value = results.companyAddress;
                console.log('✅ 公司地址已填充:', results.companyAddress);
            }
            if (results.companyPhone) {
                document.getElementById('company-phone').value = results.companyPhone;
                console.log('✅ 公司电话已填充:', results.companyPhone);
            }
            if (results.contactPerson) {
                document.getElementById('contact-person').value = results.contactPerson;
                console.log('✅ 负责人已填充:', results.contactPerson);
            }
        }

        /**
         * 执行智能切换功能
         * @function performIntelligentSwitching - 根据AI识别结果执行智能切换
         * @param {Object} results - AI分析结果
         */
        async function performIntelligentSwitching(results) {
            // 1. 根据公司信息自动切换到发票模式
            if (results.companyName || results.taxId) {
                const docTypeSelect = DOMCache.documentType || document.getElementById('document-type');
                if (docTypeSelect && docTypeSelect.value === 'receipt') {
                    docTypeSelect.value = 'invoice';
                    toggleCompanyFields('invoice');
                    console.log('AI检测到公司信息，已自动切换到发票模式');
                }
            }

            // 2. 根据货币信息自动切换货币类型
            if (results.currency) {
                const currencyCode = detectCurrencyFromText(results.currency);
                if (currencyCode && currencyCode !== AppConfig.currentCurrency) {
                    switchCurrency(currencyCode);
                    const currencySelector = DOMCache.currencySelector || document.getElementById('currency-selector');
                    if (currencySelector) {
                        currencySelector.value = currencyCode;
                    }
                    console.log(`AI检测到货币信息，已自动切换到${CurrencyConfig[currencyCode].name}`);
                }
            }

            // 3. 根据公司类型自动切换公司选择器
            if (results.companyName) {
                const companyCode = detectCompanyFromName(results.companyName);
                if (companyCode && companyCode !== AppConfig.currentCompany) {
                    AppConfig.currentCompany = companyCode;
                    const companySelector = DOMCache.companySelector || document.getElementById('company-selector');
                    if (companySelector) {
                        companySelector.value = companyCode;
                    }
                    console.log(`AI检测到公司信息，已自动切换到${companyCode}`);
                }
            }
        }

        /**
         * 从文本中检测货币类型
         * @function detectCurrencyFromText - 从文本中检测货币类型
         * @param {string} text - 包含货币信息的文本
         * @returns {string|null} 货币代码或null
         */
        function detectCurrencyFromText(text) {
            const lowerText = text.toLowerCase();

            // 检测马来西亚令吉
            if (lowerText.includes('rm') || lowerText.includes('ringgit') ||
                lowerText.includes('malaysia') || lowerText.includes('myr')) {
                return 'MYR';
            }

            // 检测人民币
            if (lowerText.includes('¥') || lowerText.includes('yuan') ||
                lowerText.includes('rmb') || lowerText.includes('cny') ||
                lowerText.includes('人民币')) {
                return 'CNY';
            }

            return null;
        }

        /**
         * 从公司名称检测公司代码
         * @function detectCompanyFromName - 从公司名称检测公司代码
         * @param {string} companyName - 公司名称
         * @returns {string|null} 公司代码或null
         */
        function detectCompanyFromName(companyName) {
            const lowerName = companyName.toLowerCase();

            // 检测GoMyHire
            if (lowerName.includes('gomyhire') || lowerName.includes('go my hire')) {
                return 'gomyhire';
            }

            // 检测Sky Mirror
            if (lowerName.includes('sky mirror') || lowerName.includes('skymirror')) {
                return 'sky-mirror';
            }

            return null;
        }

        /**
         * 从AI结果填充项目数据（支持多订单）
         * @function fillItemsFromAI - 从AI分析结果填充项目表格
         * @param {Array} items - 项目数组
         * @param {string} orderId - 订单ID（可选，用于多订单场景）
         */
        async function fillItemsFromAI(items, orderId = null) {
            console.log('🔄 开始填充AI识别的项目数据:', items.length + '个项目');

            const tbody = document.getElementById('items-tbody');

            // 根据模式决定处理方式
            if (AppConfig.multiOrderMode && orderId) {
                // 多订单模式：追加到指定订单
                await fillItemsForMultiOrder(items, orderId);
            } else {
                // 单订单模式：覆盖现有项目
                await fillItemsForSingleOrder(items);
            }

            // 更新总金额
            updateTotalAmount();
            console.log('✅ 项目数据填充完成');
        }

        /**
         * 单订单模式填充项目
         * @function fillItemsForSingleOrder - 单订单模式下填充项目
         * @param {Array} items - 项目数组
         */
        async function fillItemsForSingleOrder(items) {
            const tbody = document.getElementById('items-tbody');

            console.log('🔄 单订单模式填充项目 - 输入数据:', items);

            // 清空现有项目
            tbody.innerHTML = '';

            // 添加AI识别的项目
            items.forEach((item, index) => {
                // 修复：确保数值类型正确转换
                const quantity = parseFloat(item.quantity) || 1;
                const price = parseFloat(item.price) || 0;
                const amount = quantity * price;

                console.log(`📝 处理项目 ${index + 1}:`, {
                    描述: item.description,
                    数量原始: item.quantity,
                    数量转换: quantity,
                    价格原始: item.price,
                    价格转换: price,
                    金额计算: amount
                });

                const row = document.createElement('tr');
                const orderColumnDisplay = AppConfig.multiOrderMode ? 'table-cell' : 'none';
                const currentOrderName = AppConfig.multiOrderMode && AppConfig.multiOrderData[AppConfig.currentOrderIndex]
                    ? AppConfig.multiOrderData[AppConfig.currentOrderIndex].orderName
                    : '';

                row.innerHTML = `
                    <td class="order-column" style="display: ${orderColumnDisplay};">
                        <span class="order-badge">${currentOrderName}</span>
                    </td>
                    <td><input type="text" value="${item.description || ''}" class="item-description" title="项目描述 / Item description"></td>
                    <td><input type="number" value="${quantity}" min="1" class="item-quantity" placeholder="数量" title="数量 / Quantity"></td>
                    <td><input type="number" value="${price}" step="0.01" min="0" class="item-price" placeholder="单价" title="单价 / Price"></td>
                    <td class="item-amount">${formatCurrency(amount)}</td>
                    <td><button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">删除 / Delete</button></td>
                `;
                tbody.appendChild(row);

                // 为新行绑定事件
                const quantityInput = row.querySelector('.item-quantity');
                const priceInput = row.querySelector('.item-price');
                const descInput = row.querySelector('.item-description');

                quantityInput.addEventListener('input', () => updateItemAmount(row));
                priceInput.addEventListener('input', () => updateItemAmount(row));

                // 为描述输入框添加自动预览更新
                if (AppConfig.autoPreview) {
                    descInput.addEventListener('input', () => safeDebouncedUpdatePreview());
                }

                console.log(`✅ 项目 ${index + 1} 已添加到表格，金额: ${formatCurrency(amount)}`);
            });

            console.log(`✅ 单订单模式填充完成，共 ${items.length} 个项目`);
        }

        /**
         * 多订单模式填充项目
         * @function fillItemsForMultiOrder - 多订单模式下填充项目到指定订单
         * @param {Array} items - 项目数组
         * @param {string} orderId - 目标订单ID
         */
        async function fillItemsForMultiOrder(items, orderId) {
            // 查找目标订单
            const orderIndex = AppConfig.multiOrderData.findIndex(order => order.orderId === orderId);
            if (orderIndex === -1) {
                console.warn('⚠️ 未找到指定订单:', orderId);
                return;
            }

            // 保存当前订单数据
            if (AppConfig.currentOrderIndex !== orderIndex) {
                MultiOrderManager.saveCurrentOrderData();
            }

            // 切换到目标订单
            AppConfig.currentOrderIndex = orderIndex;

            // 将项目添加到目标订单
            const targetOrder = AppConfig.multiOrderData[orderIndex];
            targetOrder.items = targetOrder.items || [];

            // 合并项目（避免重复）
            items.forEach(newItem => {
                const existingItem = targetOrder.items.find(item =>
                    item.description === newItem.description
                );

                if (existingItem) {
                    // 更新现有项目
                    existingItem.quantity = newItem.quantity;
                    existingItem.price = newItem.price;
                    existingItem.amount = newItem.quantity * newItem.price;
                } else {
                    // 添加新项目
                    targetOrder.items.push({
                        description: newItem.description,
                        quantity: newItem.quantity,
                        price: newItem.price,
                        amount: newItem.quantity * newItem.price
                    });
                }
            });

            // 重新加载表格显示
            MultiOrderManager.loadOrderToForm(targetOrder);
            MultiOrderManager.updateOrderTabs();
            MultiOrderManager.updateCurrentOrderDisplay();

            console.log(`✅ 已将${items.length}个项目添加到订单${orderId}`);
        }

        // 创建优化的防抖预览更新函数
        const debouncedUpdatePreview = DebounceManager.debounce(updatePreview, 500, 'preview_update');

        /**
         * 性能优化的预览更新管理器
         * @description 管理预览更新的性能优化
         */
        const PreviewManager = {
            isUpdating: false,
            pendingUpdate: false,

            /**
             * 安全的预览更新
             * @function safeUpdatePreview - 安全的预览更新，避免重复调用
             */
            async safeUpdatePreview() {
                if (this.isUpdating) {
                    this.pendingUpdate = true;
                    return;
                }

                this.isUpdating = true;
                try {
                    await updatePreview();

                    // 如果有待处理的更新，再次执行
                    if (this.pendingUpdate) {
                        this.pendingUpdate = false;
                        setTimeout(() => this.safeUpdatePreview(), 100);
                    }
                } catch (error) {
                    console.error('预览更新失败:', error);
                } finally {
                    this.isUpdating = false;
                }
            }
        };

        // 创建安全的防抖预览更新函数
        const safeDebouncedUpdatePreview = DebounceManager.debounce(
            () => PreviewManager.safeUpdatePreview(),
            300,
            'safe_preview_update'
        );

        /**
         * 错误处理和性能监控系统
         * @description 统一的错误处理和性能监控
         */
        const ErrorManager = {
            errors: [],
            maxErrors: 50,

            /**
             * 记录错误
             * @function logError - 记录错误信息
             * @param {Error} error - 错误对象
             * @param {string} context - 错误上下文
             */
            logError(error, context = 'Unknown') {
                const errorInfo = {
                    timestamp: new Date().toISOString(),
                    message: error.message,
                    stack: error.stack,
                    context: context
                };

                this.errors.push(errorInfo);

                // 保持错误日志数量在限制内
                if (this.errors.length > this.maxErrors) {
                    this.errors.shift();
                }

                console.error(`[${context}] 错误:`, error);
            },

            /**
             * 安全执行函数
             * @function safeExecute - 安全执行函数，捕获错误
             * @param {Function} func - 要执行的函数
             * @param {string} context - 执行上下文
             * @param {...any} args - 函数参数
             * @returns {any} 函数执行结果或null
             */
            safeExecute(func, context, ...args) {
                try {
                    return func(...args);
                } catch (error) {
                    this.logError(error, context);
                    return null;
                }
            },

            /**
             * 获取错误统计
             * @function getErrorStats - 获取错误统计信息
             * @returns {Object} 错误统计
             */
            getErrorStats() {
                return {
                    totalErrors: this.errors.length,
                    recentErrors: this.errors.slice(-10),
                    errorsByContext: this.errors.reduce((acc, error) => {
                        acc[error.context] = (acc[error.context] || 0) + 1;
                        return acc;
                    }, {})
                };
            }
        };
        // #endregion

        // #region 多订单管理功能
        /**
         * 多订单管理器
         * @description 管理多个订单的数据和界面
         */
        const MultiOrderManager = {
            /**
             * 切换多订单模式
             * @function toggleMode - 切换单订单/多订单模式
             */
            toggleMode() {
                AppConfig.multiOrderMode = !AppConfig.multiOrderMode;
                const container = document.getElementById('multi-order-container');
                const orderColumn = document.getElementById('order-column-header');
                const orderCells = document.querySelectorAll('.order-column');

                if (AppConfig.multiOrderMode) {
                    container.style.display = 'block';
                    orderColumn.style.display = 'table-cell';
                    orderCells.forEach(cell => cell.style.display = 'table-cell');
                    this.initializeMultiOrderMode();
                    console.log('✅ 已切换到多订单模式');
                } else {
                    container.style.display = 'none';
                    orderColumn.style.display = 'none';
                    orderCells.forEach(cell => cell.style.display = 'none');
                    this.resetToSingleMode();
                    console.log('✅ 已切换到单订单模式');
                }
            },

            /**
             * 初始化多订单模式
             * @function initializeMultiOrderMode - 初始化多订单界面和数据
             */
            initializeMultiOrderMode() {
                if (AppConfig.multiOrderData.length === 0) {
                    // 创建第一个订单
                    this.createOrderFromCurrentForm();
                }
                this.updateOrderTabs();
                this.updateCurrentOrderDisplay();

                // 设置默认显示模式为合并显示
                AppConfig.displayMode = 'combined';
                const displayModeSelector = document.getElementById('display-mode');
                if (displayModeSelector) {
                    displayModeSelector.value = 'combined';
                }

                console.log('✅ 多订单模式初始化完成，默认为合并显示模式');
            },

            /**
             * 从当前表单创建订单
             * @function createOrderFromCurrentForm - 从当前表单数据创建新订单
             */
            createOrderFromCurrentForm() {
                const orderData = {
                    orderId: `ORD${String(AppConfig.orderCounter).padStart(3, '0')}`,
                    orderName: `订单${AppConfig.orderCounter}`,
                    documentNumber: document.getElementById('document-number').value || '',
                    customerName: document.getElementById('customer-name').value || '',
                    customerPhone: document.getElementById('customer-phone').value || '',
                    customerEmail: document.getElementById('customer-email').value || '',
                    items: this.getCurrentItems(),
                    notes: document.getElementById('notes').value || ''
                };

                AppConfig.multiOrderData.push(orderData);
                AppConfig.orderCounter++;
                console.log('📋 已创建新订单:', orderData.orderId);
                return orderData;
            },

            /**
             * 获取当前表格中的项目数据
             * @function getCurrentItems - 获取当前项目表格中的所有项目
             * @returns {Array} 项目数组
             */
            getCurrentItems() {
                const items = [];
                const rows = document.querySelectorAll('#items-tbody tr');

                rows.forEach(row => {
                    const description = row.querySelector('.item-description').value.trim();
                    const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
                    const price = parseFloat(row.querySelector('.item-price').value) || 0;

                    if (description || quantity > 0 || price > 0) {
                        items.push({
                            description,
                            quantity,
                            price,
                            amount: quantity * price
                        });
                    }
                });

                return items;
            },

            /**
             * 更新订单标签页
             * @function updateOrderTabs - 更新订单标签页显示
             */
            updateOrderTabs() {
                const tabsContainer = document.getElementById('order-tabs');
                if (!tabsContainer) return;

                tabsContainer.innerHTML = '';

                AppConfig.multiOrderData.forEach((order, index) => {
                    const tab = document.createElement('button');
                    tab.className = `order-tab ${index === AppConfig.currentOrderIndex ? 'active' : ''}`;
                    tab.textContent = order.orderName;
                    tab.onclick = () => this.switchToOrder(index);
                    tabsContainer.appendChild(tab);
                });
            },

            /**
             * 更新当前订单显示
             * @function updateCurrentOrderDisplay - 更新当前订单信息显示
             */
            updateCurrentOrderDisplay() {
                const currentOrder = AppConfig.multiOrderData[AppConfig.currentOrderIndex];
                if (!currentOrder) return;

                const orderDisplay = document.getElementById('current-order-display');
                const customerDisplay = document.getElementById('current-customer-display');

                if (orderDisplay) orderDisplay.textContent = currentOrder.documentNumber || currentOrder.orderId;
                if (customerDisplay) customerDisplay.textContent = currentOrder.customerName || '未填写';
            },

            /**
             * 切换到指定订单
             * @function switchToOrder - 切换到指定索引的订单
             * @param {number} orderIndex - 订单索引
             */
            switchToOrder(orderIndex) {
                if (orderIndex < 0 || orderIndex >= AppConfig.multiOrderData.length) return;

                // 保存当前订单数据
                if (AppConfig.currentOrderIndex !== orderIndex) {
                    this.saveCurrentOrderData();
                }

                // 切换到新订单
                AppConfig.currentOrderIndex = orderIndex;
                this.loadOrderToForm(AppConfig.multiOrderData[orderIndex]);
                this.updateOrderTabs();
                this.updateCurrentOrderDisplay();

                // 根据当前显示模式更新单据号码
                if (AppConfig.displayMode === 'combined') {
                    this.updateCombinedDocumentNumber();
                } else {
                    // 分别显示模式下恢复当前订单的独立单据号码
                    const currentOrder = AppConfig.multiOrderData[orderIndex];
                    const documentNumberInput = document.getElementById('document-number');
                    if (documentNumberInput && currentOrder.documentNumber) {
                        documentNumberInput.value = currentOrder.documentNumber;
                    }
                }

                console.log(`🔄 已切换到订单: ${AppConfig.multiOrderData[orderIndex].orderId}`);
            },

            /**
             * 保存当前订单数据
             * @function saveCurrentOrderData - 保存当前表单数据到订单
             */
            saveCurrentOrderData() {
                const currentOrder = AppConfig.multiOrderData[AppConfig.currentOrderIndex];
                if (!currentOrder) return;

                currentOrder.documentNumber = document.getElementById('document-number').value;
                currentOrder.customerName = document.getElementById('customer-name').value;
                currentOrder.customerPhone = document.getElementById('customer-phone').value;
                currentOrder.customerEmail = document.getElementById('customer-email').value;
                currentOrder.items = this.getCurrentItems();
                currentOrder.notes = document.getElementById('notes').value;

                console.log('💾 已保存订单数据:', currentOrder.orderId);
            },

            /**
             * 加载订单数据到表单
             * @function loadOrderToForm - 将订单数据加载到表单
             * @param {Object} orderData - 订单数据
             */
            loadOrderToForm(orderData) {
                // 填充基本信息
                document.getElementById('document-number').value = orderData.documentNumber || '';
                document.getElementById('customer-name').value = orderData.customerName || '';
                document.getElementById('customer-phone').value = orderData.customerPhone || '';
                document.getElementById('customer-email').value = orderData.customerEmail || '';
                document.getElementById('notes').value = orderData.notes || '';

                // 填充项目数据
                this.loadItemsToTable(orderData.items || []);

                // 更新总金额
                updateTotalAmount();
            },

            /**
             * 加载项目到表格
             * @function loadItemsToTable - 将项目数据加载到表格
             * @param {Array} items - 项目数组
             */
            loadItemsToTable(items) {
                const tbody = document.getElementById('items-tbody');
                tbody.innerHTML = '';

                if (items.length === 0) {
                    // 添加空行
                    addItem();
                    return;
                }

                items.forEach(item => {
                    const row = document.createElement('tr');
                    const orderColumnDisplay = AppConfig.multiOrderMode ? 'table-cell' : 'none';
                    const currentOrderName = AppConfig.multiOrderData[AppConfig.currentOrderIndex]?.orderName || '';

                    row.innerHTML = `
                        <td class="order-column" style="display: ${orderColumnDisplay};">
                            <span class="order-badge">${currentOrderName}</span>
                        </td>
                        <td><input type="text" value="${item.description || ''}" class="item-description" title="项目描述 / Item description"></td>
                        <td><input type="number" value="${item.quantity || 1}" min="1" class="item-quantity" placeholder="数量" title="数量 / Quantity"></td>
                        <td><input type="number" value="${item.price || 0}" step="0.01" min="0" class="item-price" placeholder="单价" title="单价 / Price"></td>
                        <td class="item-amount">${formatCurrency(item.amount || 0)}</td>
                        <td><button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">删除 / Delete</button></td>
                    `;

                    tbody.appendChild(row);

                    // 绑定事件
                    const quantityInput = row.querySelector('.item-quantity');
                    const priceInput = row.querySelector('.item-price');
                    const descInput = row.querySelector('.item-description');

                    quantityInput.addEventListener('input', () => updateItemAmount(row));
                    priceInput.addEventListener('input', () => updateItemAmount(row));

                    if (AppConfig.autoPreview) {
                        descInput.addEventListener('input', () => safeDebouncedUpdatePreview());
                    }
                });
            },

            /**
             * 重置到单订单模式
             * @function resetToSingleMode - 重置到单订单模式
             */
            resetToSingleMode() {
                AppConfig.multiOrderData = [];
                AppConfig.currentOrderIndex = 0;
                AppConfig.orderCounter = 1;

                // 重新加载表格，移除订单列
                const tbody = document.getElementById('items-tbody');
                const rows = tbody.querySelectorAll('tr');

                rows.forEach(row => {
                    const orderCell = row.querySelector('.order-column');
                    if (orderCell) {
                        orderCell.style.display = 'none';
                    }
                });
            },

            /**
             * 显示合并视图
             * @function showCombinedView - 在单个表格中显示所有订单的项目
             */
            showCombinedView() {
                console.log('🔄 切换到合并显示模式');

                if (AppConfig.multiOrderData.length === 0) {
                    console.warn('⚠️ 没有订单数据可显示');
                    return;
                }

                // 保存当前订单数据
                this.saveCurrentOrderData();

                // 清空表格并添加合并视图样式
                const tbody = document.getElementById('items-tbody');
                const table = document.getElementById('items-table');
                tbody.innerHTML = '';
                table.classList.add('combined-view');

                let totalAmount = 0;
                let totalItems = 0;

                // 遍历所有订单，添加项目到表格
                AppConfig.multiOrderData.forEach((order, orderIndex) => {
                    if (order.items && order.items.length > 0) {
                        order.items.forEach((item, itemIndex) => {
                            const row = document.createElement('tr');
                            const orderColumnDisplay = AppConfig.multiOrderMode ? 'table-cell' : 'none';

                            row.innerHTML = `
                                <td class="order-column" style="display: ${orderColumnDisplay};">
                                    <span class="order-badge" style="background: ${this.getOrderColor(orderIndex)};">${order.orderName}</span>
                                </td>
                                <td><input type="text" value="${item.description || ''}" class="item-description" title="项目描述 / Item description" readonly></td>
                                <td><input type="number" value="${item.quantity || 1}" min="1" class="item-quantity" placeholder="数量" title="数量 / Quantity" readonly></td>
                                <td><input type="number" value="${item.price || 0}" step="0.01" min="0" class="item-price" placeholder="单价" title="单价 / Price" readonly></td>
                                <td class="item-amount">${formatCurrency(item.amount || 0)}</td>
                                <td>
                                    <span class="order-info" style="font-size: 12px; color: #666;">
                                        ${order.customerName || '未知客户'}
                                    </span>
                                </td>
                            `;

                            tbody.appendChild(row);
                            totalAmount += item.amount || 0;
                            totalItems++;
                        });
                    }
                });

                // 使用统一的总金额计算和更新
                updateTotalAmount();

                // 生成并更新合并的单据号码
                this.updateCombinedDocumentNumber();
                const combinedDocumentNumber = this.generateCombinedDocumentNumber();

                // 更新当前订单信息显示
                const orderDisplay = document.getElementById('current-order-display');
                const customerDisplay = document.getElementById('current-customer-display');

                if (orderDisplay) {
                    orderDisplay.textContent = combinedDocumentNumber || `合并视图 (${AppConfig.multiOrderData.length}个订单)`;
                }
                if (customerDisplay) {
                    customerDisplay.textContent = `${totalItems}个项目`;
                }

                // 获取最终计算的总金额用于日志
                const finalTotalAmount = calculateTotalAmount();
                console.log(`✅ 合并显示完成: ${AppConfig.multiOrderData.length}个订单, ${totalItems}个项目, 总金额: ${formatCurrency(finalTotalAmount)}`);
                console.log(`📋 合并单据号码: ${combinedDocumentNumber}`);
            },

            /**
             * 显示分别视图
             * @function showSeparateView - 只显示当前选中订单的项目
             */
            showSeparateView() {
                console.log('🔄 切换到分别显示模式');

                if (AppConfig.multiOrderData.length === 0) {
                    console.warn('⚠️ 没有订单数据可显示');
                    return;
                }

                // 移除合并视图样式
                const table = document.getElementById('items-table');
                table.classList.remove('combined-view');

                // 加载当前订单到表单
                const currentOrder = AppConfig.multiOrderData[AppConfig.currentOrderIndex];
                if (currentOrder) {
                    this.loadOrderToForm(currentOrder);
                    this.updateCurrentOrderDisplay();

                    // 恢复当前订单的独立单据号码
                    const documentNumberInput = document.getElementById('document-number');
                    if (documentNumberInput && currentOrder.documentNumber) {
                        documentNumberInput.value = currentOrder.documentNumber;
                        console.log(`📋 恢复独立单据号码: ${currentOrder.documentNumber}`);
                    }

                    // 更新总金额显示
                    updateTotalAmount();

                    console.log(`✅ 分别显示完成: 当前显示订单 ${currentOrder.orderName}`);
                } else {
                    console.warn('⚠️ 当前订单索引无效:', AppConfig.currentOrderIndex);
                }
            },

            /**
             * 获取订单颜色
             * @function getOrderColor - 为不同订单分配不同的颜色
             * @param {number} orderIndex - 订单索引
             * @returns {string} 颜色值
             */
            getOrderColor(orderIndex) {
                const colors = [
                    '#007bff', // 蓝色
                    '#28a745', // 绿色
                    '#dc3545', // 红色
                    '#ffc107', // 黄色
                    '#6f42c1', // 紫色
                    '#fd7e14', // 橙色
                    '#20c997', // 青色
                    '#e83e8c'  // 粉色
                ];
                return colors[orderIndex % colors.length];
            },

            /**
             * 生成合并的单据号码
             * @function generateCombinedDocumentNumber - 将多个订单的单据号码合并为叠加形态
             * @returns {string} 合并后的单据号码
             */
            generateCombinedDocumentNumber() {
                if (AppConfig.multiOrderData.length === 0) {
                    return '';
                }

                const documentNumbers = AppConfig.multiOrderData
                    .map(order => order.documentNumber)
                    .filter(number => number && number.trim() !== '') // 过滤空值
                    .map(number => number.trim()); // 去除空格

                if (documentNumbers.length === 0) {
                    return '';
                }

                const combinedNumber = documentNumbers.join('/');
                console.log(`📋 生成合并单据号码: ${combinedNumber} (来自${documentNumbers.length}个订单)`);
                return combinedNumber;
            },

            /**
             * 更新合并模式下的单据号码显示
             * @function updateCombinedDocumentNumber - 更新合并模式下的单据号码字段
             */
            updateCombinedDocumentNumber() {
                if (AppConfig.displayMode === 'combined' && AppConfig.multiOrderMode) {
                    const combinedNumber = this.generateCombinedDocumentNumber();
                    const documentNumberInput = document.getElementById('document-number');

                    if (documentNumberInput && combinedNumber) {
                        documentNumberInput.value = combinedNumber;
                        console.log(`✅ 合并单据号码已更新: ${combinedNumber}`);
                    }
                }
            }
        };

        // #endregion

        // #region 项目管理功能
        /**
         * 添加新项目行
         * @function addItem - 在项目表格中添加新的项目行
         */
        function addItem() {
            const tbody = document.getElementById('items-tbody');
            const newRow = document.createElement('tr');

            // 根据多订单模式决定是否显示订单列
            const orderColumnDisplay = AppConfig.multiOrderMode ? 'table-cell' : 'none';
            const currentOrderName = AppConfig.multiOrderMode && AppConfig.multiOrderData[AppConfig.currentOrderIndex]
                ? AppConfig.multiOrderData[AppConfig.currentOrderIndex].orderName
                : '';

            newRow.innerHTML = `
                <td class="order-column" style="display: ${orderColumnDisplay};">
                    <span class="order-badge">${currentOrderName}</span>
                </td>
                <td><input type="text" placeholder="项目描述 / Item description" class="item-description" title="项目描述 / Item description"></td>
                <td><input type="number" value="1" min="1" class="item-quantity" placeholder="数量" title="数量 / Quantity"></td>
                <td><input type="number" step="0.01" min="0" class="item-price" placeholder="单价" title="单价 / Price"></td>
                <td class="item-amount">0.00</td>
                <td><button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">删除 / Delete</button></td>
            `;

            tbody.appendChild(newRow);

            // 为新行添加事件监听器
            const quantityInput = newRow.querySelector('.item-quantity');
            const priceInput = newRow.querySelector('.item-price');
            const descInput = newRow.querySelector('.item-description');

            quantityInput.addEventListener('input', () => updateItemAmount(newRow));
            priceInput.addEventListener('input', () => updateItemAmount(newRow));

            // 为描述输入框添加自动预览更新
            if (AppConfig.autoPreview) {
                descInput.addEventListener('input', () => safeDebouncedUpdatePreview());
            }

            AppConfig.itemCounter++;
        }

        /**
         * 切换多订单模式
         * @function toggleMultiOrderMode - 切换单订单/多订单模式
         */
        function toggleMultiOrderMode() {
            MultiOrderManager.toggleMode();
        }

        /**
         * 添加新订单
         * @function addNewOrder - 添加新的订单
         */
        function addNewOrder() {
            if (!AppConfig.multiOrderMode) {
                console.warn('⚠️ 请先切换到多订单模式');
                return;
            }

            // 保存当前订单数据
            if (AppConfig.multiOrderData[AppConfig.currentOrderIndex]) {
                MultiOrderManager.saveCurrentOrderData();
            }

            // 创建新订单
            const newOrder = MultiOrderManager.createOrderFromCurrentForm();
            AppConfig.currentOrderIndex = AppConfig.multiOrderData.length - 1;

            // 清空表单准备输入新订单
            clearFormForNewOrder();

            // 更新界面
            MultiOrderManager.updateOrderTabs();
            MultiOrderManager.updateCurrentOrderDisplay();

            console.log('✅ 已添加新订单:', newOrder.orderId);
        }

        /**
         * 切换显示模式
         * @function switchDisplayMode - 切换分别显示/合并显示模式
         * @param {string} mode - 显示模式 ('separate' | 'combined')
         */
        function switchDisplayMode(mode) {
            if (!AppConfig.multiOrderMode) {
                console.warn('⚠️ 请先切换到多订单模式');
                return;
            }

            if (!mode || (mode !== 'separate' && mode !== 'combined')) {
                console.error('❌ 无效的显示模式:', mode);
                return;
            }

            try {
                AppConfig.displayMode = mode;

                if (mode === 'combined') {
                    MultiOrderManager.showCombinedView();
                    console.log('🔄 已切换到合并显示模式，单据号码已合并');
                } else {
                    MultiOrderManager.showSeparateView();
                    console.log('🔄 已切换到分别显示模式，单据号码已恢复独立');
                }

                // 更新显示模式选择器
                const displayModeSelector = document.getElementById('display-mode');
                if (displayModeSelector && displayModeSelector.value !== mode) {
                    displayModeSelector.value = mode;
                }

                console.log(`🔄 已切换到${mode === 'combined' ? '合并' : '分别'}显示模式`);
            } catch (error) {
                console.error('❌ 切换显示模式失败:', error);
            }
        }

        /**
         * 清空表单准备新订单
         * @function clearFormForNewOrder - 清空表单但保留公司信息
         */
        function clearFormForNewOrder() {
            // 清空客户信息
            document.getElementById('customer-name').value = '';
            document.getElementById('customer-phone').value = '';
            document.getElementById('customer-email').value = '';
            document.getElementById('notes').value = '';

            // 生成新的单据号码
            const docType = document.getElementById('document-type').value;
            document.getElementById('document-number').value = generateDocumentNumber(docType);

            // 清空项目表格，保留一行
            const tbody = document.getElementById('items-tbody');
            const orderColumnDisplay = AppConfig.multiOrderMode ? 'table-cell' : 'none';

            tbody.innerHTML = `
                <tr>
                    <td class="order-column" style="display: ${orderColumnDisplay};"></td>
                    <td><input type="text" placeholder="项目描述 / Item description" class="item-description" title="项目描述 / Item description"></td>
                    <td><input type="number" value="1" min="1" class="item-quantity" placeholder="数量" title="数量 / Quantity"></td>
                    <td><input type="number" step="0.01" min="0" class="item-price" placeholder="单价" title="单价 / Price"></td>
                    <td class="item-amount">0.00</td>
                    <td><button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">删除 / Delete</button></td>
                </tr>
            `;

            // 重新绑定事件
            initializeItemEvents();
            updateTotalAmount();
        }

        /**
         * 删除项目行
         * @function removeItem - 删除指定的项目行
         * @param {HTMLElement} button - 删除按钮元素
         */
        function removeItem(button) {
            const row = button.closest('tr');
            const tbody = document.getElementById('items-tbody');

            // 确保至少保留一行
            if (tbody.children.length > 1) {
                row.remove();
                updateTotalAmount();
            } else {
                // 如果只剩一行，清空内容而不删除
                row.querySelector('.item-description').value = '';
                row.querySelector('.item-quantity').value = '1';
                row.querySelector('.item-price').value = '';
                updateItemAmount(row);
            }
        }

        /**
         * 清空表单
         * @function clearForm - 清空所有表单输入
         */
        function clearForm() {
            // 重置基本信息
            if (DOMCache.documentNumber) DOMCache.documentNumber.value = '';
            if (DOMCache.documentDate) DOMCache.documentDate.value = '';

            // 重置公司信息
            const companyFields = ['company-name', 'tax-id', 'company-address', 'company-phone', 'contact-person'];
            companyFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) element.value = '';
            });

            // 重置客户信息
            if (DOMCache.customerName) DOMCache.customerName.value = '';
            if (DOMCache.channel) DOMCache.channel.value = '';
            if (DOMCache.customerPhone) DOMCache.customerPhone.value = '';
            if (DOMCache.customerEmail) DOMCache.customerEmail.value = '';
            if (DOMCache.notes) DOMCache.notes.value = '';

            // 清空项目表格，只保留一行
            const tbody = DOMCache.itemsTbody || document.getElementById('items-tbody');
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td><input type="text" placeholder="项目描述 / Item description" class="item-description" title="项目描述 / Item description"></td>
                        <td><input type="number" value="1" min="1" class="item-quantity" placeholder="数量" title="数量 / Quantity"></td>
                        <td><input type="number" step="0.01" min="0" class="item-price" placeholder="单价" title="单价 / Price"></td>
                        <td class="item-amount">0.00</td>
                        <td><button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">删除 / Delete</button></td>
                    </tr>
                `;
            }

            // 重新绑定事件
            initializeItemEvents();

            // 更新总金额
            updateTotalAmount();

            // 清空预览
            document.getElementById('document-container').innerHTML = `
                <div class="empty-preview-message">
                    请填写表单信息并点击"更新预览"按钮<br>
                    Please fill in the form and click "Update Preview" button
                </div>
            `;

            // 重新生成单据号码
            const docType = document.getElementById('document-type').value;
            document.getElementById('document-number').value = generateDocumentNumber(docType);
        }

        /**
         * 初始化项目输入事件
         * @function initializeItemEvents - 为项目输入框绑定事件监听器
         */
        function initializeItemEvents() {
            const rows = document.querySelectorAll('#items-tbody tr');

            rows.forEach(row => {
                const quantityInput = row.querySelector('.item-quantity');
                const priceInput = row.querySelector('.item-price');
                const descInput = row.querySelector('.item-description');

                quantityInput.addEventListener('input', () => updateItemAmount(row));
                priceInput.addEventListener('input', () => updateItemAmount(row));

                // 为描述输入框添加自动预览更新
                if (AppConfig.autoPreview) {
                    descInput.addEventListener('input', () => safeDebouncedUpdatePreview());
                }
            });
        }
        // #endregion

        // #region 模板渲染功能
        /**
         * 发票模板类 - 简化版
         * @description 处理发票文档的渲染逻辑
         */
        class InvoiceTemplate {
            /**
             * 渲染发票模板
             * @function render - 根据数据渲染发票
             * @param {Object} data - 发票数据
             * @returns {string} 渲染后的HTML字符串
             */
            static render(data) {
                const company = AppConfig.currentCompany;
                const companyInfo = CompanyInfo[company];

                return `
                    <div class="invoice-container">
                        ${this.renderHeader(company, companyInfo, data)}
                        ${this.renderTitle('发票 / INVOICE', data.documentNumber, data.date)}
                        ${this.renderCompanyInfo(data)}
                        ${this.renderCustomerInfo(data)}
                        ${this.renderItemsTable(data.items)}
                        ${this.renderTotal(data.total)}
                        ${this.renderNotes(data.notes)}
                        ${this.renderElectronicSignature()}
                        ${this.renderFooter(company)}
                        ${this.renderStamp(company)}
                    </div>
                `;
            }

            /**
             * 渲染页眉
             * @function renderHeader - 渲染文档页眉
             * @param {string} company - 公司代码
             * @param {Object} companyInfo - 公司信息
             * @param {Object} data - 表单数据
             * @returns {string} 页眉HTML
             */
            static renderHeader(company, companyInfo, data) {
                const headerImage = ImageBase64.getHeader(company);
                const logo = ImageBase64.getLogo(company);

                if (headerImage) {
                    return `
                        <div class="document-header-image-container">
                            <img src="${headerImage}" alt="页眉图片">
                        </div>
                    `;
                }

                // 显示图片占位区域
                return `
                    <div class="document-header">
                        <div class="image-placeholder header-placeholder">
                            页眉图片区域 / Header Image Area<br>
                            <small style="font-size: 10px; opacity: 0.7;">建议尺寸: 794x90px / Recommended: 794x90px</small>
                            ${logo ? `<br><img src="${logo}" alt="公司标志" style="max-height: 40px; margin-top: 5px;">` : '<br><small style="font-size: 10px;">公司标志占位 / Logo Placeholder</small>'}
                        </div>
                    </div>
                `;
            }

            /**
             * 渲染标题
             * @function renderTitle - 渲染文档标题
             * @param {string} title - 文档标题
             * @param {string} number - 单据号码
             * @param {string} date - 日期
             * @returns {string} 标题HTML
             */
            static renderTitle(title, number, date) {
                return `
                    <div style="text-align: center; margin: 20px 0;">
                        <h1 style="color: var(--primary-color); margin-bottom: 10px; font-size: 24px;">${title}</h1>
                        <div style="display: flex; justify-content: space-between; margin-top: 15px; font-size: 14px;">
                            <div><strong>单据号码 / Document Number:</strong> ${number}</div>
                            <div><strong>日期 / Date:</strong> ${date}</div>
                        </div>
                    </div>
                `;
            }

            /**
             * 渲染公司信息
             * @function renderCompanyInfo - 渲染公司信息（智能显示）
             * @param {Object} data - 表单数据
             * @returns {string} 公司信息HTML
             */
            static renderCompanyInfo(data) {
                const fields = [];

                if (data.companyName) {
                    fields.push(`<div><strong>公司名称 / Company Name:</strong> ${data.companyName}</div>`);
                }
                if (data.taxId) {
                    fields.push(`<div><strong>税号 / Tax ID:</strong> ${data.taxId}</div>`);
                }
                if (data.companyAddress) {
                    fields.push(`<div><strong>公司地址 / Company Address:</strong> ${data.companyAddress}</div>`);
                }
                if (data.companyPhone) {
                    fields.push(`<div><strong>公司电话 / Company Phone:</strong> ${data.companyPhone}</div>`);
                }
                if (data.contactPerson) {
                    fields.push(`<div><strong>负责人 / Contact Person:</strong> ${data.contactPerson}</div>`);
                }

                if (fields.length === 0) return '';

                return `
                    <div style="margin: 15px 0; padding: 12px; border: 1px solid var(--border-color); border-radius: 6px; background: #f9f9f9;">
                        <h3 style="margin-top: 0; margin-bottom: 10px; color: var(--dark-color); font-size: 16px;">公司信息 / Company Information</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 13px;">
                            ${fields.join('')}
                        </div>
                    </div>
                `;
            }

            /**
             * 渲染客户信息
             * @function renderCustomerInfo - 渲染客户信息（智能显示）
             * @param {Object} data - 客户数据
             * @returns {string} 客户信息HTML
             */
            static renderCustomerInfo(data) {
                const fields = [];

                if (data.customerName) {
                    fields.push(`<div><strong>客户名称 / Customer Name:</strong> ${data.customerName}</div>`);
                }
                if (data.channel) {
                    fields.push(`<div><strong>渠道 / Channel:</strong> ${data.channel}</div>`);
                }
                if (data.customerPhone) {
                    fields.push(`<div><strong>客户电话 / Customer Phone:</strong> ${data.customerPhone}</div>`);
                }
                if (data.customerEmail) {
                    fields.push(`<div><strong>客户邮箱 / Customer Email:</strong> ${data.customerEmail}</div>`);
                }

                if (fields.length === 0) return '';

                return `
                    <div style="margin: 15px 0; padding: 12px; border: 1px solid var(--border-color); border-radius: 6px;">
                        <h3 style="margin-top: 0; margin-bottom: 10px; color: var(--dark-color); font-size: 16px;">客户信息 / Customer Information</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 13px;">
                            ${fields.join('')}
                        </div>
                    </div>
                `;
            }
            /**
             * 渲染项目表格
             * @function renderItemsTable - 渲染项目明细表格（响应式字体）
             * @param {Array} items - 项目数组
             * @returns {string} 项目表格HTML
             */
            static renderItemsTable(items) {
                if (items.length === 0) return '';

                // 根据项目数量调整字体大小
                const fontSize = items.length > 10 ? '11px' : items.length > 5 ? '12px' : '13px';
                const padding = items.length > 10 ? '6px' : items.length > 5 ? '8px' : '10px';

                const currencySymbol = getCurrentCurrencySymbol();
                const tableRows = items.map((item, index) => `
                    <tr>
                        <td style="text-align: center;">${index + 1}</td>
                        <td>${item.description}</td>
                        <td style="text-align: center;">${item.quantity}</td>
                        <td style="text-align: right;">${currencySymbol} ${formatCurrency(item.price)}</td>
                        <td style="text-align: right;">${currencySymbol} ${formatCurrency(item.amount)}</td>
                    </tr>
                `).join('');

                return `
                    <div style="margin: 15px 0;">
                        <h3 style="color: var(--dark-color); font-size: 16px; margin-bottom: 8px;">项目明细 / Item Details</h3>
                        <table style="width: 100%; border-collapse: collapse; font-size: ${fontSize};">
                            <thead>
                                <tr style="background-color: var(--light-color);">
                                    <th style="padding: ${padding}; border: 1px solid var(--border-color); text-align: center; width: 8%;">序号 / No.</th>
                                    <th style="padding: ${padding}; border: 1px solid var(--border-color); text-align: left; width: 40%;">项目描述 / Description</th>
                                    <th style="padding: ${padding}; border: 1px solid var(--border-color); text-align: center; width: 12%;">数量 / Qty</th>
                                    <th style="padding: ${padding}; border: 1px solid var(--border-color); text-align: right; width: 20%;">单价 / Unit Price</th>
                                    <th style="padding: ${padding}; border: 1px solid var(--border-color); text-align: right; width: 20%;">金额 / Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${tableRows}
                            </tbody>
                        </table>
                    </div>
                `;
            }

            /**
             * 渲染总金额
             * @function renderTotal - 渲染总金额信息
             * @param {number} total - 总金额
             * @returns {string} 总金额HTML
             */
            static renderTotal(total) {
                const currencySymbol = getCurrentCurrencySymbol();

                // 详细调试信息
                console.log(`💰 渲染总金额开始 - 输入参数:`, {
                    原始total: total,
                    类型: typeof total,
                    是否为数字: !isNaN(total),
                    货币符号: currencySymbol,
                    当前货币: AppConfig.currentCurrency,
                    多订单模式: AppConfig.multiOrderMode,
                    显示模式: AppConfig.displayMode
                });

                // 确保总金额有效
                let safeTotal = 0;
                if (total !== null && total !== undefined && !isNaN(total)) {
                    safeTotal = parseFloat(total);
                } else {
                    console.warn(`⚠️ 总金额无效，使用默认值0 - 原始值: ${total}`);
                }

                const displayTotal = formatCurrency(safeTotal);

                console.log(`💰 渲染总金额完成 - 最终显示:`, {
                    安全total: safeTotal,
                    格式化结果: displayTotal,
                    完整显示: `${currencySymbol} ${displayTotal}`
                });

                return `
                    <div style="margin: 15px 0; text-align: right; position: relative; z-index: 100; clear: both;">
                        <div class="total-amount-container" style="display: inline-block; padding: 12px 20px; border: 2px solid #1e40af; border-radius: 6px; background-color: rgba(255, 255, 255, 0.95); box-shadow: 0 2px 4px rgba(0,0,0,0.1); min-width: 200px; z-index: 100;">
                            <h3 style="margin: 0; color: #1e40af; font-size: 18px; font-weight: bold; white-space: nowrap; text-shadow: 1px 1px 2px rgba(255,255,255,0.8);">总金额 / Total Amount: ${currencySymbol} ${displayTotal}</h3>
                        </div>
                    </div>
                `;
            }

            /**
             * 渲染备注
             * @function renderNotes - 渲染备注信息
             * @param {string} notes - 备注内容
             * @returns {string} 备注HTML
             */
            static renderNotes(notes) {
                if (!notes) return '';

                return `
                    <div style="margin: 15px 0;">
                        <h3 style="color: var(--dark-color); font-size: 16px; margin-bottom: 8px;">备注 / Notes</h3>
                        <div style="padding: 10px; border: 1px solid var(--border-color); border-radius: 6px; background-color: #f9f9f9; font-size: 13px;">
                            ${notes}
                        </div>
                    </div>
                `;
            }

            /**
             * 渲染电子生成标识
             * @function renderElectronicSignature - 渲染电子生成提示
             * @returns {string} 电子生成标识HTML
             */
            static renderElectronicSignature() {
                return `
                    <div class="electronic-signature">
                        此文档为电子生成 / This document is electronically generated
                    </div>
                `;
            }

            /**
             * 渲染页脚
             * @function renderFooter - 渲染文档页脚（带占位区域）
             * @param {string} company - 公司代码
             * @returns {string} 页脚HTML
             */
            static renderFooter(company) {
                const footerImage = ImageBase64.getFooter(company);

                if (footerImage) {
                    return `
                        <div class="unified-document-footer company-footer-image-container">
                            <img src="${footerImage}" alt="页脚图片">
                        </div>
                    `;
                }

                return `
                    <div class="document-footer">
                        <div class="image-placeholder footer-placeholder">
                            页脚图片区域 / Footer Image Area<br>
                            <small style="font-size: 10px; opacity: 0.7;">建议尺寸: 794x70px / Recommended: 794x70px</small>
                        </div>
                    </div>
                `;
            }

            /**
             * 渲染印章
             * @function renderStamp - 渲染公司印章（带占位区域）
             * @param {string} company - 公司代码
             * @returns {string} 印章HTML
             */
            static renderStamp(company) {
                const stamp = ImageBase64.getStamp(company);

                if (stamp) {
                    return `
                        <div class="company-stamp">
                            <img src="${stamp}" alt="公司印章">
                        </div>
                    `;
                }

                return `
                    <div class="image-placeholder stamp-placeholder">
                        印章区域<br>Stamp Area<br>
                        <small style="font-size: 9px; opacity: 0.7;">120x120px</small>
                    </div>
                `;
            }
        }

        /**
         * 收据模板类
         * @description 处理收据文档的渲染逻辑
         */
        class ReceiptTemplate {
            /**
             * 渲染收据模板
             * @function render - 根据数据渲染收据
             * @param {Object} data - 收据数据
             * @returns {string} 渲染后的HTML字符串
             */
            static render(data) {
                const company = AppConfig.currentCompany;
                const companyInfo = CompanyInfo[company];

                return `
                    <div class="receipt-container">
                        ${InvoiceTemplate.renderHeader(company, companyInfo, data)}
                        ${InvoiceTemplate.renderTitle('收据 / RECEIPT', data.documentNumber, data.date)}
                        ${InvoiceTemplate.renderCompanyInfo(data)}
                        ${this.renderReceiptInfo(data)}
                        ${InvoiceTemplate.renderItemsTable(data.items)}
                        ${InvoiceTemplate.renderTotal(data.total)}
                        ${InvoiceTemplate.renderNotes(data.notes)}
                        ${InvoiceTemplate.renderElectronicSignature()}
                        ${InvoiceTemplate.renderFooter(company)}
                        ${InvoiceTemplate.renderStamp(company)}
                    </div>
                `;
            }

            /**
             * 渲染收据信息
             * @function renderReceiptInfo - 渲染收据特有信息（智能显示）
             * @param {Object} data - 收据数据
             * @returns {string} 收据信息HTML
             */
            static renderReceiptInfo(data) {
                const fields = [];

                if (data.customerName) {
                    fields.push(`<div><strong>付款人 / Payer:</strong> ${data.customerName}</div>`);
                }
                if (data.channel) {
                    fields.push(`<div><strong>渠道 / Channel:</strong> ${data.channel}</div>`);
                }
                if (data.customerPhone) {
                    fields.push(`<div><strong>联系方式 / Contact:</strong> ${data.customerPhone}</div>`);
                }
                if (data.customerEmail) {
                    fields.push(`<div><strong>邮箱 / Email:</strong> ${data.customerEmail}</div>`);
                }

                if (fields.length === 0) return '';

                return `
                    <div style="margin: 15px 0; padding: 12px; border: 1px solid var(--border-color); border-radius: 6px;">
                        <h3 style="margin-top: 0; margin-bottom: 10px; color: var(--dark-color); font-size: 16px;">收款信息 / Payment Information</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 13px;">
                            ${fields.join('')}
                        </div>
                        <div style="margin-top: 12px; padding: 8px; background-color: var(--light-color); border-radius: 4px; font-size: 13px;">
                            <strong>收款确认 / Payment Confirmation:</strong> 已收到上述款项，特此开具收据。/ The above payment has been received. This receipt is issued accordingly.
                        </div>
                    </div>
                `;
            }
        }
        // #endregion

        // #region 主要功能函数
        /**
         * 更新预览
         * @function updatePreview - 更新文档预览
         */
        function updatePreview() {
            const data = collectFormData();
            const container = document.getElementById('document-container');

            console.log(`🔄 更新预览 - 数据收集:`, {
                项目数量: data.items.length,
                总金额原始值: data.total,
                总金额格式化: formatCurrency(data.total),
                货币符号: getCurrentCurrencySymbol(),
                文档类型: data.documentType,
                模式: AppConfig.multiOrderMode ? '多订单' : '单订单',
                显示模式: AppConfig.displayMode || '标准'
            });

            // 自动填充单据号码（如果为空）
            if (!document.getElementById('document-number').value) {
                document.getElementById('document-number').value = data.documentNumber;
            }

            // 自动填充日期（如果为空）
            if (!document.getElementById('document-date').value) {
                document.getElementById('document-date').value = data.date;
            }

            let html = '';

            if (data.documentType === 'invoice') {
                html = InvoiceTemplate.render(data);
            } else {
                html = ReceiptTemplate.render(data);
            }

            container.innerHTML = html;

            // 同时更新导出容器（确保导出和预览一致）
            const exportContainer = document.getElementById('document-preview');
            if (exportContainer && exportContainer !== container) {
                exportContainer.innerHTML = html;
                console.log(`✅ 预览更新完成 - 同时更新了预览容器和导出容器`);
            } else {
                console.log(`✅ 预览更新完成 - 使用统一容器`);
            }

            // 更新配置
            AppConfig.currentDocumentType = data.documentType;
        }

        /**
         * 导出为PDF
         * @function exportToPDF - 将文档导出为PDF文件
         */
        async function exportToPDF() {
            // 检查依赖库是否可用
            if (typeof html2canvas === 'undefined') {
                alert('PDF导出功能不可用：html2canvas库未加载。\n请确保网络连接正常或使用在线版本。');
                return;
            }

            if (typeof window.jspdf === 'undefined') {
                alert('PDF导出功能不可用：jsPDF库未加载。\n请确保网络连接正常或使用在线版本。');
                return;
            }

            const container = document.getElementById('document-preview');
            const data = collectFormData();

            console.log(`📄 PDF导出 - 数据收集完成:`, {
                项目数量: data.items.length,
                总金额: formatCurrency(data.total),
                单据号码: data.documentNumber,
                模式: AppConfig.multiOrderMode ? '多订单' : '单订单',
                显示模式: AppConfig.displayMode || '标准'
            });

            if (!data.items.length) {
                alert('请至少添加一个项目后再导出');
                return;
            }

            try {
                // 临时移除缩放效果以获得更好的导出质量
                const originalTransform = container.style.transform;
                container.style.transform = 'scale(1)';

                // 使用html2canvas生成canvas
                const canvas = await html2canvas(container, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff'
                });

                // 恢复原始缩放
                container.style.transform = originalTransform;

                // 创建PDF
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF('p', 'mm', 'a4');

                // 计算图片尺寸
                const imgWidth = 210; // A4宽度
                const imgHeight = (canvas.height * imgWidth) / canvas.width;

                // 添加图片到PDF
                const imgData = canvas.toDataURL('image/png');
                pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

                // 生成文件名
                const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
                const docTypeName = data.documentType === 'invoice' ? '发票_Invoice' : '收据_Receipt';
                const filename = `${docTypeName}_${data.documentNumber || timestamp}.pdf`;

                // 保存PDF文件
                pdf.save(filename);

                console.log('PDF导出成功:', filename);
            } catch (error) {
                console.error('PDF导出失败:', error);
                alert('PDF导出失败，请检查浏览器控制台获取详细信息');
            }
        }

        /**
         * 导出为图片
         * @function exportToImage - 将文档导出为PNG图片
         */
        async function exportToImage() {
            // 检查依赖库是否可用
            if (typeof html2canvas === 'undefined') {
                alert('图片导出功能不可用：html2canvas库未加载。\n请确保网络连接正常或使用在线版本。');
                return;
            }

            const container = document.getElementById('document-preview');
            const data = collectFormData();

            console.log(`🖼️ 图片导出 - 数据收集完成:`, {
                项目数量: data.items.length,
                总金额: formatCurrency(data.total),
                单据号码: data.documentNumber,
                模式: AppConfig.multiOrderMode ? '多订单' : '单订单',
                显示模式: AppConfig.displayMode || '标准'
            });

            if (!data.items.length) {
                alert('请至少添加一个项目后再导出');
                return;
            }

            try {
                // 临时移除缩放效果以获得更好的导出质量
                const originalTransform = container.style.transform;
                container.style.transform = 'scale(1)';

                // 使用html2canvas生成canvas
                const canvas = await html2canvas(container, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff'
                });

                // 恢复原始缩放
                container.style.transform = originalTransform;

                // 生成文件名
                const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
                const docTypeName = data.documentType === 'invoice' ? '发票_Invoice' : '收据_Receipt';
                const filename = `${docTypeName}_${data.documentNumber || timestamp}.png`;

                // 创建下载链接
                const link = document.createElement('a');
                link.download = filename;
                link.href = canvas.toDataURL('image/png');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                console.log('图片导出成功:', filename);
            } catch (error) {
                console.error('图片导出失败:', error);
                alert('图片导出失败，请检查浏览器控制台获取详细信息');
            }
        }
        // #endregion

        // #region 事件监听器和初始化
        /**
         * 检查外部依赖库是否加载成功
         * @function checkExternalDependencies - 检查外部库的可用性
         */
        function checkExternalDependencies() {
            const dependencies = {
                html2canvas: typeof html2canvas !== 'undefined',
                jsPDF: typeof window.jspdf !== 'undefined'
            };

            if (!dependencies.html2canvas) {
                console.warn('html2canvas未加载，图片导出功能将不可用');
            }

            if (!dependencies.jsPDF) {
                console.warn('jsPDF未加载，PDF导出功能将不可用');
            }

            return dependencies;
        }

        /**
         * 初始化应用程序
         * @function initializeApp - 初始化所有事件监听器和默认值
         */
        function initializeApp() {
            // 检查外部依赖
            const dependencies = checkExternalDependencies();

            // 初始化DOM缓存
            DOMCache.initCache();

            // 设置默认日期
            const today = new Date().toISOString().split('T')[0];
            if (DOMCache.documentDate) {
                DOMCache.documentDate.value = today;
            }

            // 从localStorage恢复货币设置
            const savedCurrency = localStorage.getItem('smartoffice_currency');
            if (savedCurrency && CurrencyConfig[savedCurrency]) {
                AppConfig.currentCurrency = savedCurrency;
                if (DOMCache.currencySelector) {
                    DOMCache.currencySelector.value = savedCurrency;
                }
            }

            // 初始化货币显示
            updateAllCurrencyDisplays();

            // 初始化字段显示状态
            const initialDocType = DOMCache.documentType ? DOMCache.documentType.value : 'receipt';
            toggleCompanyFields(initialDocType);

            // 初始化项目事件
            initializeItemEvents();

            // 绑定表单变化事件（自动预览）
            const formElements = [
                'document-type',
                'company-selector',
                'currency-selector',
                'company-name',
                'tax-id',
                'company-address',
                'company-phone',
                'contact-person',
                'document-number',
                'document-date',
                'customer-name',
                'channel',
                'customer-phone',
                'customer-email',
                'notes'
            ];

            formElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('change', () => {
                        if (id === 'company-selector') {
                            AppConfig.currentCompany = element.value;
                        } else if (id === 'currency-selector') {
                            switchCurrency(element.value);
                        } else if (id === 'document-type') {
                            toggleCompanyFields(element.value);
                        }

                        // 自动预览更新（使用优化的防抖函数）
                        if (AppConfig.autoPreview) {
                            safeDebouncedUpdatePreview();
                        }
                    });

                    // 为文本输入框添加输入事件（使用优化的防抖函数）
                    if (element.type === 'text' || element.type === 'email' || element.tagName === 'TEXTAREA') {
                        element.addEventListener('input', () => {
                            if (AppConfig.autoPreview) {
                                safeDebouncedUpdatePreview();
                            }
                        });
                    }
                }
            });

            // 绑定文档类型变化事件
            document.getElementById('document-type').addEventListener('change', function() {
                const newType = this.value;
                const numberInput = document.getElementById('document-number');

                // 如果单据号码为空或者是旧格式，生成新的单据号码
                if (!numberInput.value ||
                    (newType === 'invoice' && numberInput.value.startsWith('RCP')) ||
                    (newType === 'receipt' && numberInput.value.startsWith('INV'))) {
                    numberInput.value = generateDocumentNumber(newType);
                }
            });

            // 初始化单据号码
            const initialType = document.getElementById('document-type').value;
            document.getElementById('document-number').value = generateDocumentNumber(initialType);

            console.log('发票/收据生成器初始化完成 / Invoice/Receipt Generator Initialized');
        }

        /**
         * 页面加载完成后初始化
         */
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();

            // 显示欢迎信息
            console.log('='.repeat(50));
            console.log('发票/收据生成器 - SmartOffice 2.0');
            console.log('独立HTML版本 - 支持离线使用');
            console.log('功能: 发票生成、收据生成、PDF导出、图片导出');
            console.log('='.repeat(50));
        });

        /**
         * 全局错误处理
         */
        window.addEventListener('error', function(event) {
            console.error('发生错误:', event.error);
            // 可以在这里添加用户友好的错误提示
        });

        /**
         * 导出全局函数供HTML调用
         */
        window.addItem = addItem;
        window.removeItem = removeItem;
        window.clearForm = clearForm;
        window.updatePreview = updatePreview;
        window.exportToPDF = exportToPDF;
        window.exportToImage = exportToImage;
        window.toggleAIFillPanel = toggleAIFillPanel;
        window.processAIFill = processAIFill;
        window.clearAIInput = clearAIInput;
        window.switchCurrency = switchCurrency;
        window.toggleCompanyFields = toggleCompanyFields;
        // #endregion

        /**
         * 版本信息和开发说明
         * @description
         * 发票/收据生成器 v3.0 - 双语显示专业版
         *
         * 新增特性 v3.0:
         * - 🌐 双语并列显示模式（中文 / English）
         * - 🖼️ 图片占位区域显示（页眉、页脚、印章）
         * - 📋 发票字段增强（公司信息、税号等）
         * - 🧠 智能字段显示（空字段自动隐藏）
         * - � 电子生成标识
         * - � Material Design风格优化
         * - � A4单页布局优化
         * - 📱 响应式内容适配
         *
         * 核心特性:
         * - 独立HTML文件，支持file://协议
         * - 传统script标签架构，最大兼容性
         * - 内置图片资源管理（base64格式）
         * - 支持发票和收据两种文档类型
         * - 动态项目管理，自动计算金额
         * - PDF和图片导出功能
         * - 完整的中英双语界面
         *
         * 界面特点:
         * - 双语并列显示，无需切换
         * - Material Design设计规范
         * - 图片占位区域可视化
         * - 智能字段隐藏机制
         * - 电子生成水印标识
         *
         * 使用方法:
         * 1. 选择文档类型（发票/收据）
         * 2. 选择公司信息
         * 3. 填写公司详细信息（可选）
         * 4. 填写客户信息（支持实时预览）
         * 5. 添加项目明细（自动计算总金额）
         * 6. 实时查看预览效果
         * 7. 使用"导出PDF"或"导出图片"保存文档
         *
         * 响应式设计:
         * - 桌面端：双列布局，最佳编辑体验
         * - 平板端：优化间距，适中预览比例
         * - 移动端：单列布局，触摸友好界面
         * - 智能字体缩放，确保单页显示
         *
         * 图片资源:
         * - 可通过ImageBase64.updateImage()方法更新图片
         * - 支持logo、header、footer、stamp四种类型
         * - 图片格式为base64编码字符串
         * - 空图片显示占位区域
         *
         * 技术特点:
         * - 防抖机制避免频繁更新
         * - 智能字段显示逻辑
         * - 自适应布局，多设备兼容
         * - A4纸张严格控制
         * - 离线可用，无需网络连接
         */
    </script>
</body>
</html>
